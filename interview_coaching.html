<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Interview Coaching - CareerScopes</title>
    <link rel="stylesheet" href="./css/styles.css" />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
    />
    <style>
      .header {
        padding: 20px 0;
        background-color: #fff;
      }

      .header .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      /* Interview Coaching Specific Styles */
      .interview-hero {
        background: linear-gradient(135deg, #7fb9b1 0%, #a8d5d1 100%);
        padding: 80px 0;
        border-radius: 20px;
        margin: 20px;
        position: relative;
        overflow: hidden;
      }

      .interview-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url('./images/Foreground.jpeg');
        background-size: cover;
        background-position: center;
        opacity: 0.3;
        z-index: 1;
      }

      .interview-hero .container {
        position: relative;
        z-index: 2;
        text-align: center;
      }

      .interview-hero h1 {
        color: white;
        font-size: 48px;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        font-style: italic;
      }

      .main-content {
        padding: 60px 0;
        text-align: center;
      }

      .main-title {
        font-size: 36px;
        font-weight: 700;
        color: #333;
        margin-bottom: 60px;
        line-height: 1.2;
      }

      .book-session-title {
        font-size: 32px;
        font-weight: 700;
        color: #7fb9b1;
        margin-bottom: 50px;
        text-align: center;
        position: relative;
      }

      .book-session-title::after {
        content: '';
        position: absolute;
        bottom: -15px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background: linear-gradient(90deg, #7fb9b1, #00d4aa);
        border-radius: 2px;
      }

      .service-box {
        background-color: #f5f5f5;
        border: 2px solid #ddd;
        border-radius: 15px;
        padding: 30px;
        margin: 30px auto;
        max-width: 600px;
        position: relative;
        text-align: center;
      }

      .service-box h3 {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin-bottom: 20px;
        text-align: center;
      }

      .service-list {
        list-style: none;
        padding: 0;
        margin: 0 0 30px 0;
        text-align: left;
      }

      .service-list li {
        font-size: 16px;
        color: #333;
        margin-bottom: 10px;
        padding-left: 20px;
        position: relative;
      }

      .service-list li::before {
        content: '•';
        position: absolute;
        left: 0;
        color: #7fb9b1;
        font-weight: bold;
      }

      .book-btn {
        background-color: #00d4aa;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s;
        box-shadow: 0 2px 8px rgba(0, 212, 170, 0.3);
        display: block;
        margin: 0 auto;
      }

      .book-btn:hover {
        background-color: #00b894;
        box-shadow: 0 4px 12px rgba(0, 212, 170, 0.4);
        transform: translateY(-1px);
      }

      .consultation-section {
        padding: 60px 0;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 40px;
      }

      .phone-icon {
        width: 80px;
        height: 80px;
        border: 3px solid #333;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36px;
      }

      .book-consultation-btn {
        background-color: #00d4aa;
        color: white;
        border: none;
        padding: 20px 40px;
        border-radius: 10px;
        font-size: 18px;
        font-weight: 700;
        cursor: pointer;
        transition: background-color 0.3s;
      }

      .book-consultation-btn:hover {
        background-color: #00b894;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .header .container {
          flex-direction: column;
          gap: 20px;
        }

        .interview-hero h1 {
          font-size: 36px;
        }

        .main-title {
          font-size: 28px;
        }

        .interview-hero {
          margin: 10px;
          padding: 60px 0;
        }

        .service-box {
          margin: 20px;
          padding: 20px;
        }

        .book-btn {
          margin: 0 auto;
          display: block;
        }

        .consultation-section {
          flex-direction: column;
          gap: 20px;
          padding: 40px 20px;
        }

        .phone-icon {
          width: 60px;
          height: 60px;
          font-size: 28px;
        }

        .book-consultation-btn {
          padding: 15px 30px;
          font-size: 16px;
        }
      }

      @media (max-width: 576px) {
        .interview-hero h1 {
          font-size: 28px;
        }

        .main-title {
          font-size: 24px;
        }

        .book-session-title {
          font-size: 24px;
        }

        .service-box h3 {
          font-size: 20px;
        }
      }
    </style>
  </head>
  <body>
    <!-- Header Section -->
    <div class="header">
      <div class="container">
        <div class="logo">
          <a href="./index.html">
            <img src="./images/logo.jpg" alt="careerScops" />
          </a>
        </div>
        <nav>
          <ul>
            <li><a href="./index.html">Home</a></li>
            <li><a href="./about-us.html">About us</a></li>
            <li><a href="./interview_coaching.html" class="active">Services</a></li>
            <li><a href="./testimonials.html">Testimonials</a></li>
          </ul>
        </nav>
        <div class="auth-buttons">
          <a href="./login.html" class="login-btn">Login</a>
          <a href="./register.html" class="register-btn">Register</a>
        </div>
      </div>
    </div>

    <!-- Interview Coaching Hero Section -->
    <section class="interview-hero">
      <div class="container">
        <h1>Interview Coaching</h1>
      </div>
    </section>

    <!-- Main Content Section -->
    <section class="main-content">
      <div class="container">
        <h2 class="main-title">Ace your next interview with<br>confidence</h2>
        
        <h3 class="book-session-title">Book Session</h3>
        
        <!-- What's Included Section -->
        <div class="service-box">
          <h3>What's Included?</h3>
          <ul class="service-list">
            <li>Mock interviews</li>
            <li>Question prep</li>
            <li>Feedback</li>
          </ul>
          <button class="book-btn" onclick="bookFreeConsultation()">Book Free Consultation</button>
        </div>

        <!-- Session Types Section -->
        <div class="service-box">
          <h3>Session Types</h3>
          <ul class="service-list">
            <li>Behavioral</li>
            <li>Technical</li>
            <li>Panel</li>
            <li>Video interviews</li>
          </ul>
          <button class="book-btn" onclick="bookConsultation()">Book Consultation</button>
        </div>
      </div>
    </section>

    <!-- Book Consultation Section -->
    <section class="consultation-section">
      <div class="container">
        <div style="display: flex; justify-content: center; align-items: center; gap: 40px; flex-wrap: wrap;">
          <div class="phone-icon">
            📞
          </div>
          <button class="book-consultation-btn" onclick="bookMainConsultation()">
            Book<br>Consultation
          </button>
        </div>
      </div>
    </section>

    <!-- Footer Section -->
    <footer>
      <div class="container">
        <div class="footer-top">
          <div class="footer-info">
            <h3>CareerScopes</h3>
            <p>
              Career Scopes would like to acknowledge the traditional custodians
              of the land on which we are based, the Aboriginal people of the
              Kulin Nation.
            </p>
            <p>
              We also pay our respects to their Elders, past, present and
              future, whose cultures and customs have nurtured and continue to
              nurture this land.
            </p>
            <p>We pay our respects to Elders past, present and future.</p>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h4>About</h4>
              <ul>
                <li><a href="#">JOBS</a></li>
                <li><a href="#">FAQS</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h4>Legal</h4>
              <ul>
                <li><a href="#">Terms and Conditions</a></li>
                <li><a href="#">Privacy Policy</a></li>
                <li><a href="#">Cookie Policy</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h4>Contact us</h4>
              <div class="social-icons">
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                    ></path>
                  </svg>
                  <span>0421 594 877</span>
                </a>
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                    ></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                  <span style="font-size: 0.75rem"
                    ><EMAIL></span
                  >
                </a>
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"
                    ></path>
                    <rect x="2" y="9" width="4" height="12"></rect>
                    <circle cx="4" cy="4" r="2"></circle>
                  </svg>
                  <span style="font-size: 1rem"
                    >www.linkedin.com/in/lijesla</span
                  >
                </a>
              </div>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <div class="flags">
            <img
              src="./images/image 13.png"
              alt="Aboriginal Flag"
              class="flag"
            />
            <img
              src="./images/image 14.png"
              alt="Torres Strait Islander Flag"
              class="flag"
            />
          </div>
          <div class="copyright">
            © CareerScope - All Rights Reserved. Love From Australia
          </div>
        </div>
      </div>
    </footer>

    <script>
      function bookFreeConsultation() {
        alert('Booking free consultation...');
      }

      function bookConsultation() {
        alert('Booking consultation...');
      }

      function bookMainConsultation() {
        alert('Booking main consultation...');
      }
    </script>
  </body>
</html>
