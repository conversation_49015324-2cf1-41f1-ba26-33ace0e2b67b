<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LinkedIn Optimization - CareerScopes</title>
    <link rel="stylesheet" href="./css/styles.css" />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
    />
    <style>
      .header {
        padding: 20px 0;
        background-color: #fff;
      }

      .header .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      /* LinkedIn Optimization Specific Styles */
      .linkedin-hero {
        background: linear-gradient(135deg, #7fb9b1 0%, #a8d5d1 100%);
        padding: 80px 0;
        border-radius: 20px;
        margin: 20px;
        position: relative;
        overflow: hidden;
      }

      .linkedin-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url('./images/Foreground.jpeg');
        background-size: cover;
        background-position: center;
        opacity: 0.3;
        z-index: 1;
      }

      .linkedin-hero .container {
        position: relative;
        z-index: 2;
        text-align: center;
      }

      .linkedin-hero h1 {
        color: white;
        font-size: 48px;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        font-style: italic;
      }

      .main-content {
        padding: 60px 0;
        text-align: center;
      }

      .linkedin-section {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 40px;
        margin-bottom: 60px;
        flex-wrap: wrap;
      }

      .linkedin-logo {
        width: 120px;
        height: 120px;
        background-color: #0077b5;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 60px;
        font-weight: bold;
        text-decoration: none;
      }

      .linkedin-text {
        text-align: left;
      }

      .linkedin-text h2 {
        font-size: 36px;
        font-weight: 700;
        color: #333;
        margin-bottom: 20px;
        line-height: 1.2;
      }

      .optimize-btn {
        background-color: #00d4aa;
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s;
        box-shadow: 0 2px 8px rgba(0, 212, 170, 0.3);
      }

      .optimize-btn:hover {
        background-color: #00b894;
        box-shadow: 0 4px 12px rgba(0, 212, 170, 0.4);
        transform: translateY(-1px);
      }

      .what-we-do {
        margin: 80px 0;
      }

      .what-we-do h3 {
        font-size: 32px;
        font-weight: 700;
        color: #333;
        margin-bottom: 50px;
      }

      .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 40px;
        max-width: 800px;
        margin: 0 auto;
      }

      .service-card {
        background-color: #c8e6e1;
        border-radius: 20px;
        padding: 40px 30px;
        text-align: center;
        transition: transform 0.3s ease;
      }

      .service-card:hover {
        transform: translateY(-5px);
      }

      .service-card h4 {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin-bottom: 30px;
      }

      .service-card .book-btn {
        background-color: #00d4aa;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s;
        box-shadow: 0 2px 8px rgba(0, 212, 170, 0.3);
      }

      .service-card .book-btn:hover {
        background-color: #00b894;
        box-shadow: 0 4px 12px rgba(0, 212, 170, 0.4);
        transform: translateY(-1px);
      }

      .contact-form {
        background-color: #f5f5f5;
        border: 2px solid #ddd;
        border-radius: 15px;
        padding: 40px;
        margin: 60px auto;
        max-width: 600px;
      }

      .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
      }

      .form-group {
        display: flex;
        flex-direction: column;
      }

      .form-group label {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
      }

      .form-group input,
      .form-group textarea {
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 16px;
        font-family: 'Inter', sans-serif;
      }

      .form-group textarea {
        resize: vertical;
        min-height: 100px;
      }

      .send-btn {
        background-color: #00d4aa;
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s;
        box-shadow: 0 2px 8px rgba(0, 212, 170, 0.3);
        display: block;
        margin: 30px auto 0;
      }

      .send-btn:hover {
        background-color: #00b894;
        box-shadow: 0 4px 12px rgba(0, 212, 170, 0.4);
        transform: translateY(-1px);
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .header .container {
          flex-direction: column;
          gap: 20px;
        }

        .linkedin-hero h1 {
          font-size: 36px;
        }

        .linkedin-hero {
          margin: 10px;
          padding: 60px 0;
        }

        .linkedin-section {
          flex-direction: column;
          gap: 20px;
        }

        .linkedin-text {
          text-align: center;
        }

        .linkedin-text h2 {
          font-size: 28px;
        }

        .linkedin-logo {
          width: 100px;
          height: 100px;
          font-size: 50px;
        }

        .services-grid {
          grid-template-columns: 1fr;
          gap: 20px;
        }

        .form-row {
          grid-template-columns: 1fr;
        }

        .contact-form {
          margin: 40px 20px;
          padding: 30px 20px;
        }
      }

      @media (max-width: 576px) {
        .linkedin-hero h1 {
          font-size: 28px;
        }

        .linkedin-text h2 {
          font-size: 24px;
        }

        .what-we-do h3 {
          font-size: 28px;
        }

        .service-card {
          padding: 30px 20px;
        }

        .service-card h4 {
          font-size: 20px;
        }
      }
    </style>
  </head>
  <body>
    <!-- Header Section -->
    <div class="header">
      <div class="container">
        <div class="logo">
          <a href="./index.html">
            <img src="./images/logo.jpg" alt="careerScops" />
          </a>
        </div>
        <nav>
          <ul>
            <li><a href="./index.html">Home</a></li>
            <li><a href="./about-us.html">About us</a></li>
            <li><a href="./linkedin_optimization.html" class="active">Services</a></li>
            <li><a href="./testimonials.html">Testimonials</a></li>
          </ul>
        </nav>
        <div class="auth-buttons">
          <a href="./login.html" class="login-btn">Login</a>
          <a href="./register.html" class="register-btn">Register</a>
        </div>
      </div>
    </div>

    <!-- LinkedIn Optimization Hero Section -->
    <section class="linkedin-hero">
      <div class="container">
        <h1>LinkedIn Optimization</h1>
      </div>
    </section>

    <!-- Main Content Section -->
    <section class="main-content">
      <div class="container">
        <!-- LinkedIn Section -->
        <div class="linkedin-section">
          <div class="linkedin-logo">
            in
          </div>
          <div class="linkedin-text">
            <h2>Stand out to recruiters<br>and employers</h2>
            <button class="optimize-btn" onclick="optimizeProfile()">Optimise Profile</button>
          </div>
        </div>

        <!-- What We Do Section -->
        <div class="what-we-do">
          <h3>What We Do ?</h3>
          <div class="services-grid">
            <div class="service-card">
              <h4>Free<br>Profile Review</h4>
              <button class="book-btn" onclick="bookOptimisation()">Book Optimisation</button>
            </div>
            <div class="service-card">
              <h4>Complete Rewrite</h4>
              <button class="book-btn" onclick="bookOptimisation()">Book Optimisation</button>
            </div>
          </div>
        </div>

        <!-- Contact Form -->
        <div class="contact-form">
          <form id="contactForm" onsubmit="sendMessage(event)">
            <div class="form-row">
              <div class="form-group">
                <label for="firstName">First Name</label>
                <input type="text" id="firstName" name="firstName" required>
              </div>
              <div class="form-group">
                <label for="lastName">Last Name</label>
                <input type="text" id="lastName" name="lastName" required>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
              </div>
              <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" name="phone" placeholder="+61 000 000 000">
              </div>
            </div>
            <div class="form-group">
              <label for="message">Message</label>
              <textarea id="message" name="message" placeholder="Write your message" required></textarea>
            </div>
            <button type="submit" class="send-btn">Send Message</button>
          </form>
        </div>
      </div>
    </section>

    <!-- Footer Section -->
    <footer>
      <div class="container">
        <div class="footer-top">
          <div class="footer-info">
            <h3>CareerScopes</h3>
            <p>
              Career Scopes would like to acknowledge the traditional custodians
              of the land on which we are based, the Aboriginal people of the
              Kulin Nation.
            </p>
            <p>
              We also pay our respects to their Elders, past, present and
              future, whose cultures and customs have nurtured and continue to
              nurture this land.
            </p>
            <p>We pay our respects to Elders past, present and future.</p>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h4>About</h4>
              <ul>
                <li><a href="#">JOBS</a></li>
                <li><a href="#">FAQS</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h4>Legal</h4>
              <ul>
                <li><a href="#">Terms and Conditions</a></li>
                <li><a href="#">Privacy Policy</a></li>
                <li><a href="#">Cookie Policy</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h4>Contact us</h4>
              <div class="social-icons">
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                    ></path>
                  </svg>
                  <span>0421 594 877</span>
                </a>
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                    ></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                  <span style="font-size: 0.75rem"
                    ><EMAIL></span
                  >
                </a>
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"
                    ></path>
                    <rect x="2" y="9" width="4" height="12"></rect>
                    <circle cx="4" cy="4" r="2"></circle>
                  </svg>
                  <span style="font-size: 1rem"
                    >www.linkedin.com/in/lijesla</span
                  >
                </a>
              </div>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <div class="flags">
            <img
              src="./images/image 13.png"
              alt="Aboriginal Flag"
              class="flag"
            />
            <img
              src="./images/image 14.png"
              alt="Torres Strait Islander Flag"
              class="flag"
            />
          </div>
          <div class="copyright">
            © CareerScope - All Rights Reserved. Love From Australia
          </div>
        </div>
      </div>
    </footer>

    <script>
      function optimizeProfile() {
        alert('Optimizing LinkedIn profile...');
      }

      function bookOptimisation() {
        alert('Booking optimization service...');
      }

      function sendMessage(event) {
        event.preventDefault();
        alert('Message sent successfully!');
      }
    </script>
  </body>
</html>
