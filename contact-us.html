<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Home</title>
    <link rel="stylesheet" href="./css/normalize.css" />
    <link rel="stylesheet" href="./css/styles.css" />
    <style>
      main container {
        display: block;
        max-width: 100vw;
        min-height: 100vh;
        position: relative;
      }

      main container video {
        position: absolute;
        object-fit: cover;
        z-index: -1;
        width: 100%;
        height: 100%;
      }

      /* Just styling the content of the div, the *magic* in the previous rules */
      main container .caption {
        z-index: 1;
        text-align: center;
        padding: 10px;
        inset: 0;

        display: flex;
        justify-content: center;
        align-items: center;
        background-size: cover;
      }

      .contact-container {
        padding: 2rem;
        width: 100%;
        max-width: 1200px;
        background-color: #111;
        border-radius: 20px;
        overflow: hidden;
        display: flex;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        height: 100%;
      }

      /* Contact Form */
      .contact-form {
        flex: 1;
        padding: 60px 40px;
      }

      .contact-form h1 {
        font-size: 42px;
        font-weight: 700;
        margin-bottom: 15px;
        color: #fff;
      }

      .contact-form p {
        font-size: 16px;
        color: #ccc;
        margin-bottom: 30px;
      }

      .form-row {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
      }

      .form-group {
        width: 100%;
        margin-bottom: 15px;
      }

      input,
      textarea {
        box-sizing: border-box;
        width: 100%;
        padding: 15px;
        background-color: #1a1a1a;
        border: 1px solid #333;
        border-radius: 8px;
        color: #fff;
        font-family: "Inter", sans-serif;
        font-size: 16px;
        outline: none;
        transition: border-color 0.3s;
      }

      input::placeholder,
      textarea::placeholder {
        color: #777;
      }

      input:focus,
      textarea:focus {
        border-color: #b8e0d2;
      }

      textarea {
        resize: none;
        min-height: 120px;
      }

      .submit-btn {
        width: 100%;
        padding: 15px;
        background-color: #b8e0d2;
        color: #111;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: background-color 0.3s;
      }

      .submit-btn:hover {
        background-color: #9ed4c2;
      }

      .arrow {
        margin-left: 8px;
        font-size: 20px;
      }

      /* Contact Image */
      .contact-image {
        flex: 1;
        background-color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 40px;
      }

      .contact-image img {
        max-width: 100%;
        height: auto;
      }

      /* Back Button */
      .back-btn {
        position: absolute;
        bottom: 40px;
        left: 40px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.3s;
      }

      .back-btn:hover {
        background-color: rgba(255, 255, 255, 0.3);
      }

      /* Responsive Design */
      @media (max-width: 992px) {
        .contact-container {
          flex-direction: column;
          max-width: 600px;
        }

        .contact-form {
          padding: 40px 30px;
        }

        .contact-image {
          padding: 30px;
        }
      }

      @media (max-width: 576px) {
        .form-row {
          flex-direction: column;
          gap: 15px;
        }

        .contact-form h1 {
          font-size: 32px;
        }

        .back-btn {
          bottom: 20px;
          left: 20px;
        }
      }

      select {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 16px;
        color: #333;
        transition: border-color 0.3s;
      }
      select:focus {
        outline: none;
        border-color: #7fb9b1;
      }
      .select-wrapper {
        position: relative;
      }

      .select-wrapper::after {
        content: "▼";
        font-size: 12px;
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #777;
        pointer-events: none;
      }

      select {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        background-color: #fff;
        cursor: pointer;
      }
      select {
        padding: 10px 12px;
        font-size: 14px;
      }
      label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
        color: #555;
        text-align: left;
      }
    </style>
  </head>
  <body>
    <header style="background-color: #8ec3b6; position: unset">
      <container class="container">
        <div class="logo">
          <a href="./index.html">
            <img src="./images/logo_white.png" alt="careerScops" />
          </a>
        </div>
        <nav>
          <ul>
            <li><a href="./index.html">Home</a></li>
            <li><a href="./about-us.html">About us</a></li>
            <li>
              <a href="./testimonials.html">Testimonials</a>
            </li>
            <li><a href="./contact-us.html" class="active">Contact Us</a></li>
          </ul>
        </nav>
        <div class="auth-buttons">
          <a href="./login.html" class="login-btn">Login</a>
          <a href="./register.html" class="register-btn">Register</a>
        </div>
      </container>
    </header>
    <main>
      <container>
        <video autoplay muted loop onloadstart="this.playbackRate = 0.25;">
          <source src="./images/animated-background.mp4" type="video/mp4" />
        </video>
        <div class="caption">
          <div class="contact-container">
            <div class="contact-form">
              <h1>Contact Us</h1>
              <p>Have questions? Reach out to us for more information.</p>

              <form>
                <div class="form-row">
                  <div class="form-group">
                    <input
                      type="text"
                      id="firstName"
                      placeholder="First Name"
                      required
                    />
                  </div>
                  <div class="form-group">
                    <input
                      type="text"
                      id="lastName"
                      placeholder="Last Name"
                      required
                    />
                  </div>
                </div>

                <div class="form-group">
                  <input type="email" id="email" placeholder="Email" required />
                </div>

                <div class="form-group">
                  <label for="interest">Select Of Interest</label>
                  <div class="select-wrapper">
                    <select id="interest" required>
                      <option value="career-coaching">Career Coaching</option>
                      <option value="resume-tailoring">Resume Tailoring</option>
                      <option value="psychometric-testing">
                        Psychometric Testing
                      </option>
                      <option value="networking">Effective Networking</option>
                      <option value="interview-coaching">
                        Interview Coaching
                      </option>
                      <option value="employment-searching">
                        Employment Searching
                      </option>
                      <option value="linkedin-profiling">
                        LinkedIn Profiling
                      </option>
                      <option value="packages">Packages</option>
                    </select>
                  </div>
                </div>

                <div class="form-group">
                  <label for="state">State*</label>
                  <div class="select-wrapper">
                    <select id="state" required>
                      <option value="victoria">Victoria</option>
                      <option value="new-south-wales">New South Wales</option>
                      <option value="queensland">Queensland</option>
                      <option value="western-australia">
                        Western Australia
                      </option>
                      <option value="south-australia">South Australia</option>
                      <option value="tasmania">Tasmania</option>
                      <option value="northern-territory">
                        Northern Territory
                      </option>
                      <option value="act">Australian Capital Territory</option>
                    </select>
                  </div>
                </div>

                <div class="form-group">
                  <textarea
                    id="message"
                    placeholder="Message"
                    rows="5"
                    required
                  ></textarea>
                </div>

                <button type="submit" class="submit-btn">
                  Let's Talk! <span class="arrow">→</span>
                </button>
              </form>
            </div>

            <div class="contact-image">
              <img
                src="/placeholder.svg?height=400&width=400"
                alt="Contact illustration"
              />
            </div>
          </div>
        </div>
      </container>
    </main>
  </body>
</html>
