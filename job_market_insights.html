<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Job Market Insights - CareerScopes</title>
    <link rel="stylesheet" href="./css/styles.css" />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
    />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
      .header {
        padding: 20px 0;
        background-color: #fff;
      }

      .header .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      /* Job Market Insights Specific Styles */
      .insights-hero {
        background: linear-gradient(135deg, #7fb9b1 0%, #a8d5d1 100%);
        padding: 80px 0;
        border-radius: 20px;
        margin: 20px;
        position: relative;
        overflow: hidden;
      }

      .insights-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url('./images/Foreground.jpeg');
        background-size: cover;
        background-position: center;
        opacity: 0.3;
        z-index: 1;
      }

      .insights-hero .container {
        position: relative;
        z-index: 2;
        text-align: center;
      }

      .insights-hero h1 {
        color: white;
        font-size: 48px;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        font-style: italic;
      }

      .main-content {
        padding: 60px 0;
      }

      .trending-section {
        text-align: center;
        margin-bottom: 60px;
      }

      .trending-section h2 {
        font-size: 36px;
        font-weight: 700;
        color: #333;
        margin-bottom: 50px;
      }

      .insights-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
        max-width: 1000px;
        margin: 0 auto 40px;
      }

      .insight-card {
        background-color: #c8e6e1;
        border-radius: 20px;
        padding: 40px 30px;
        text-align: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        min-height: 200px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }

      .insight-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }

      .insight-card h3 {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin-bottom: 30px;
        line-height: 1.3;
      }

      .explore-btn {
        background-color: #00d4aa;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 212, 170, 0.3);
        align-self: center;
        width: fit-content;
      }

      .explore-btn:hover {
        background-color: #00b894;
        box-shadow: 0 4px 12px rgba(0, 212, 170, 0.4);
        transform: translateY(-1px);
      }

      .chart-section {
        background-color: #f8f9fa;
        padding: 60px 0;
        margin-top: 60px;
      }

      .chart-container {
        max-width: 800px;
        margin: 0 auto;
        text-align: center;
      }

      .chart-title {
        font-size: 32px;
        font-weight: 700;
        color: #00a085;
        margin-bottom: 10px;
      }

      .chart-subtitle {
        font-size: 18px;
        color: #666;
        margin-bottom: 40px;
      }

      .chart-wrapper {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        position: relative;
        height: 400px;
      }

      #demandChart {
        max-height: 100%;
        width: 100%;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .header .container {
          flex-direction: column;
          gap: 20px;
        }

        .insights-hero h1 {
          font-size: 36px;
        }

        .insights-hero {
          margin: 10px;
          padding: 60px 0;
        }

        .trending-section h2 {
          font-size: 28px;
        }

        .chart-title {
          font-size: 28px;
        }

        .chart-subtitle {
          font-size: 16px;
        }

        .insights-grid {
          grid-template-columns: 1fr;
          gap: 20px;
          padding: 0 20px;
        }

        .insight-card {
          padding: 30px 20px;
          min-height: 180px;
        }

        .insight-card h3 {
          font-size: 20px;
        }

        .chart-wrapper {
          padding: 20px;
          height: 350px;
        }
      }

      @media (max-width: 576px) {
        .insights-hero h1 {
          font-size: 28px;
        }

        .trending-section h2 {
          font-size: 24px;
        }

        .chart-title {
          font-size: 24px;
        }

        .insight-card {
          padding: 25px 15px;
          min-height: 160px;
        }

        .insight-card h3 {
          font-size: 18px;
          margin-bottom: 20px;
        }

        .chart-wrapper {
          padding: 15px;
          height: 300px;
        }
      }

      /* Additional styling for better visual hierarchy */
      .insights-grid .insight-card:nth-child(4) {
        grid-column: 1 / -1;
        max-width: 300px;
        margin: 0 auto;
      }

      @media (min-width: 769px) {
        .insights-grid {
          grid-template-columns: repeat(3, 1fr);
        }
        
        .insights-grid .insight-card:nth-child(4) {
          grid-column: 2;
          grid-row: 2;
        }
      }
    </style>
  </head>
  <body>
    <!-- Header Section -->
    <div class="header">
      <div class="container">
        <div class="logo">
          <a href="./index.html">
            <img src="./images/logo.jpg" alt="careerScops" />
          </a>
        </div>
        <nav>
          <ul>
            <li><a href="./index.html">Home</a></li>
            <li><a href="./about-us.html">About us</a></li>
            <li><a href="./linkedin_optimization.html">Services</a></li>
            <li><a href="./testimonials.html">Testimonials</a></li>
          </ul>
        </nav>
        <div class="auth-buttons">
          <a href="./login.html" class="login-btn">Login</a>
          <a href="./register.html" class="register-btn">Register</a>
        </div>
      </div>
    </div>

    <!-- Job Market Insights Hero Section -->
    <section class="insights-hero">
      <div class="container">
        <h1>Job Market Insights</h1>
      </div>
    </section>

    <!-- Main Content Section -->
    <section class="main-content">
      <div class="container">
        <!-- Trending Career Insights Section -->
        <div class="trending-section">
          <h2>Trending Career Insights</h2>
          <div class="insights-grid">
            <div class="insight-card">
              <h3>Tech Industry<br>Growth</h3>
              <button class="explore-btn" onclick="exploreInsight('tech')">Explore More</button>
            </div>
            <div class="insight-card">
              <h3>Remote Work<br>Trends</h3>
              <button class="explore-btn" onclick="exploreInsight('remote')">Explore More</button>
            </div>
            <div class="insight-card">
              <h3>Salary<br>Benchmarks</h3>
              <button class="explore-btn" onclick="exploreInsight('salary')">Explore More</button>
            </div>
            <div class="insight-card">
              <h3>Emerging<br>Roles</h3>
              <button class="explore-btn" onclick="exploreInsight('emerging')">Explore More</button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Chart Section -->
    <section class="chart-section">
      <div class="container">
        <div class="chart-container">
          <h3 class="chart-title">Industry Demand by Sector</h3>
          <p class="chart-subtitle">Job Market Growth Analysis 2025</p>
          <div class="chart-wrapper">
            <canvas id="demandChart"></canvas>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer Section -->
    <footer>
      <div class="container">
        <div class="footer-top">
          <div class="footer-info">
            <h3>CareerScopes</h3>
            <p>
              Career Scopes would like to acknowledge the traditional custodians
              of the land on which we are based, the Aboriginal people of the
              Kulin Nation.
            </p>
            <p>
              We also pay our respects to their Elders, past, present and
              future, whose cultures and customs have nurtured and continue to
              nurture this land.
            </p>
            <p>We pay our respects to Elders past, present and future.</p>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h4>About</h4>
              <ul>
                <li><a href="#">JOBS</a></li>
                <li><a href="#">FAQS</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h4>Legal</h4>
              <ul>
                <li><a href="#">Terms and Conditions</a></li>
                <li><a href="#">Privacy Policy</a></li>
                <li><a href="#">Cookie Policy</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h4>Contact us</h4>
              <div class="social-icons">
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                    ></path>
                  </svg>
                  <span>0421 594 877</span>
                </a>
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                    ></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                  <span style="font-size: 0.75rem"
                    ><EMAIL></span
                  >
                </a>
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"
                    ></path>
                    <rect x="2" y="9" width="4" height="12"></rect>
                    <circle cx="4" cy="4" r="2"></circle>
                  </svg>
                  <span style="font-size: 1rem"
                    >www.linkedin.com/in/lijesla</span
                  >
                </a>
              </div>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <div class="flags">
            <img
              src="./images/image 13.png"
              alt="Aboriginal Flag"
              class="flag"
            />
            <img
              src="./images/image 14.png"
              alt="Torres Strait Islander Flag"
              class="flag"
            />
          </div>
          <div class="copyright">
            © CareerScope - All Rights Reserved. Love From Australia
          </div>
        </div>
      </div>
    </footer>

    <script>
      // Chart.js configuration for Industry Demand Chart
      const ctx = document.getElementById('demandChart').getContext('2d');
      const demandChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['Technology', 'Healthcare', 'Financial Services', 'Education Services', 'Manufacturing', 'Retail'],
          datasets: [{
            label: 'Demand Index',
            data: [85, 72, 68, 58, 45, 42],
            backgroundColor: [
              '#00d4aa',
              '#00c4a0',
              '#00b496',
              '#00a48c',
              '#009482',
              '#008478'
            ],
            borderColor: [
              '#00c4a0',
              '#00b496',
              '#00a48c',
              '#009482',
              '#008478',
              '#00746e'
            ],
            borderWidth: 1,
            borderRadius: 8,
            borderSkipped: false,
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              titleColor: 'white',
              bodyColor: 'white',
              borderColor: '#00d4aa',
              borderWidth: 1,
              cornerRadius: 8,
              displayColors: false,
              callbacks: {
                label: function(context) {
                  return `Demand Index: ${context.parsed.y}`;
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              grid: {
                color: 'rgba(0, 0, 0, 0.1)',
                drawBorder: false
              },
              ticks: {
                color: '#666',
                font: {
                  size: 12
                }
              },
              title: {
                display: true,
                text: 'Demand Index',
                color: '#333',
                font: {
                  size: 14,
                  weight: 'bold'
                }
              }
            },
            x: {
              grid: {
                display: false,
                drawBorder: false
              },
              ticks: {
                color: '#666',
                font: {
                  size: 11
                },
                maxRotation: 45,
                minRotation: 0
              }
            }
          },
          animation: {
            duration: 2000,
            easing: 'easeInOutQuart'
          }
        }
      });

      // JavaScript functions for interactivity
      function exploreInsight(type) {
        const insights = {
          tech: 'Technology sector shows 85% growth with high demand for AI, cybersecurity, and cloud computing roles.',
          remote: 'Remote work trends indicate 70% of companies now offer flexible work arrangements.',
          salary: 'Salary benchmarks show 15% average increase across tech roles in 2025.',
          emerging: 'Emerging roles include AI Ethics Specialist, Sustainability Manager, and Data Privacy Officer.'
        };
        
        alert(`${insights[type]}\n\nExploring detailed insights for this category...`);
      }

      // Add smooth scrolling to chart section
      document.addEventListener('DOMContentLoaded', function() {
        // Animate chart on scroll
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              demandChart.update();
            }
          });
        });
        
        observer.observe(document.getElementById('demandChart'));
      });
    </script>
  </body>
</html>
