<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Your Profile - Career Scopes</title>
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        /* Container */
        .container {
            width: 90%;
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        header {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 100;
        }
        
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
        
        .logo img {
            height: 40px;
            width: auto;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            align-items: center;
        }
        
        .nav-links li {
            margin-left: 30px;
        }
        
        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: #5acfbc;
        }
        
        .back-btn {
            padding: 8px 20px;
            border: 2px solid #5acfbc;
            border-radius: 5px;
            background-color: transparent;
            color: #5acfbc;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
        }
        
        .back-btn:hover {
            background-color: #5acfbc;
            color: white;
        }
        
        /* Main Content */
        .main-content {
            padding: 120px 0 60px;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* Progress Bar */
        .progress-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #5acfbc;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            text-align: center;
            color: #666;
            font-size: 0.9rem;
        }
        
        /* Form Container */
        .form-container {
            background-color: white;
            border-radius: 10px;
            padding: 40px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        }
        
        /* Form Sections */
        .form-section {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }
        
        .form-section.active {
            display: block;
        }
        
        .form-section h2 {
            color: #333;
            margin-bottom: 25px;
            font-size: 1.8rem;
            border-bottom: 2px solid #5acfbc;
            padding-bottom: 10px;
        }
        
        /* Form Groups */
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #5acfbc;
            box-shadow: 0 0 0 2px rgba(90, 207, 188, 0.2);
        }
        
        textarea.form-control {
            min-height: 120px;
            resize: vertical;
        }
        
        /* Dynamic Lists */
        .dynamic-list {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background-color: #f8f9fa;
        }
        
        .list-item {
            background-color: white;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            position: relative;
        }
        
        .remove-item {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        
        .add-item-btn {
            background-color: #5acfbc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        
        .add-item-btn:hover {
            background-color: #48b0a0;
        }
        
        /* Skills Section */
        .skills-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .skill-category {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }
        
        .skill-category h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .skill-input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .skill-input {
            flex: 1;
        }
        
        .skill-level {
            width: 120px;
        }
        
        .skill-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        
        .skill-tag {
            background-color: #5acfbc;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .skill-tag .remove-skill {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 0.8rem;
        }
        
        /* Navigation Buttons */
        .form-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .nav-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 1rem;
        }
        
        .btn-prev {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-prev:hover {
            background-color: #5a6268;
        }
        
        .btn-next {
            background-color: #5acfbc;
            color: white;
        }
        
        .btn-next:hover {
            background-color: #48b0a0;
        }
        
        .btn-submit {
            background-color: #28a745;
            color: white;
        }
        
        .btn-submit:hover {
            background-color: #218838;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .skills-container {
                grid-template-columns: 1fr;
            }
            
            .page-header h1 {
                font-size: 2rem;
            }
            
            .form-container {
                padding: 20px;
            }
            
            .nav-links {
                display: none;
            }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
     Header 
    <header>
        <div class="container">
            <nav>
                <div class="logo">
                    <img src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/pTa5dpOT.jpg-W70FxPN1uKTlHpcoxGxDeqItnFxB6x.jpeg" alt="Career Scopes Logo">
                </div>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="#" class="back-btn">← Back to Dashboard</a></li>
                </ul>
            </nav>
        </div>
    </header>

     Main Content 
    <main class="main-content">
        <div class="container">
            <div class="page-header">
                <h1>Complete Your Professional Profile</h1>
                <p>Help us create the perfect CV by providing your professional information. This data will be used to generate your personalized resume and career recommendations.</p>
            </div>

             Progress Bar 
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">Step 1 of 6: Personal Information</div>
            </div>

             Form Container 
            <div class="form-container">
                <form id="profileForm">
                     Section 1: Personal Information 
                    <div class="form-section active" data-section="1">
                        <h2>Personal Information</h2>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="firstName" class="form-label">First Name *</label>
                                <input type="text" id="firstName" name="firstName" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="lastName" class="form-label">Last Name *</label>
                                <input type="text" id="lastName" name="lastName" class="form-control" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" id="email" name="email" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="phone" class="form-label">Phone Number *</label>
                                <input type="tel" id="phone" name="phone" class="form-control" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="city" class="form-label">City</label>
                                <input type="text" id="city" name="city" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="country" class="form-label">Country</label>
                                <input type="text" id="country" name="country" class="form-control">
                            </div>
                        </div>
                        <div class="form-group full-width">
                            <label for="linkedinUrl" class="form-label">LinkedIn Profile URL</label>
                            <input type="url" id="linkedinUrl" name="linkedinUrl" class="form-control" placeholder="https://linkedin.com/in/yourprofile">
                        </div>
                        <div class="form-group full-width">
                            <label for="portfolioUrl" class="form-label">Portfolio/Website URL</label>
                            <input type="url" id="portfolioUrl" name="portfolioUrl" class="form-control" placeholder="https://yourportfolio.com">
                        </div>
                    </div>

                     Section 2: Professional Summary 
                    <div class="form-section" data-section="2">
                        <h2>Professional Summary</h2>
                        <div class="form-group">
                            <label for="jobTitle" class="form-label">Current/Desired Job Title *</label>
                            <input type="text" id="jobTitle" name="jobTitle" class="form-control" placeholder="e.g., Software Engineer, Marketing Manager" required>
                        </div>
                        <div class="form-group">
                            <label for="professionalSummary" class="form-label">Professional Summary *</label>
                            <textarea id="professionalSummary" name="professionalSummary" class="form-control" placeholder="Write a brief summary of your professional background, key skills, and career objectives..." required></textarea>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="yearsExperience" class="form-label">Years of Experience</label>
                                <select id="yearsExperience" name="yearsExperience" class="form-control">
                                    <option value="">Select experience level</option>
                                    <option value="0-1">0-1 years (Entry Level)</option>
                                    <option value="2-3">2-3 years</option>
                                    <option value="4-6">4-6 years</option>
                                    <option value="7-10">7-10 years</option>
                                    <option value="10+">10+ years (Senior Level)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="industry" class="form-label">Industry</label>
                                <input type="text" id="industry" name="industry" class="form-control" placeholder="e.g., Technology, Healthcare, Finance">
                            </div>
                        </div>
                    </div>

                     Section 3: Work Experience 
                    <div class="form-section" data-section="3">
                        <h2>Work Experience</h2>
                        <div class="dynamic-list" id="workExperienceList">
                            <div class="list-item">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Job Title *</label>
                                        <input type="text" name="workTitle[]" class="form-control" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Company *</label>
                                        <input type="text" name="workCompany[]" class="form-control" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Start Date</label>
                                        <input type="month" name="workStartDate[]" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">End Date</label>
                                        <input type="month" name="workEndDate[]" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Job Description</label>
                                    <textarea name="workDescription[]" class="form-control" placeholder="Describe your responsibilities and achievements..."></textarea>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="add-item-btn" onclick="addWorkExperience()">+ Add Another Position</button>
                    </div>

                     Section 4: Education 
                    <div class="form-section" data-section="4">
                        <h2>Education</h2>
                        <div class="dynamic-list" id="educationList">
                            <div class="list-item">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Degree/Qualification *</label>
                                        <input type="text" name="eduDegree[]" class="form-control" placeholder="e.g., Bachelor of Science" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Field of Study</label>
                                        <input type="text" name="eduField[]" class="form-control" placeholder="e.g., Computer Science">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Institution *</label>
                                        <input type="text" name="eduInstitution[]" class="form-control" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Graduation Year</label>
                                        <input type="number" name="eduYear[]" class="form-control" min="1950" max="2030">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">GPA/Grade (Optional)</label>
                                    <input type="text" name="eduGrade[]" class="form-control" placeholder="e.g., 3.8/4.0 or First Class">
                                </div>
                            </div>
                        </div>
                        <button type="button" class="add-item-btn" onclick="addEducation()">+ Add Another Education</button>
                    </div>

                     Section 5: Skills & Certifications 
                    <div class="form-section" data-section="5">
                        <h2>Skills & Certifications</h2>
                        <div class="skills-container">
                            <div class="skill-category">
                                <h3>Technical Skills</h3>
                                <div class="skill-input-group">
                                    <input type="text" id="technicalSkillInput" class="form-control skill-input" placeholder="Enter a technical skill">
                                    <select id="technicalSkillLevel" class="form-control skill-level">
                                        <option value="Beginner">Beginner</option>
                                        <option value="Intermediate">Intermediate</option>
                                        <option value="Advanced">Advanced</option>
                                        <option value="Expert">Expert</option>
                                    </select>
                                    <button type="button" class="add-item-btn" onclick="addSkill('technical')">Add</button>
                                </div>
                                <div class="skill-tags" id="technicalSkills"></div>
                            </div>
                            
                            <div class="skill-category">
                                <h3>Soft Skills</h3>
                                <div class="skill-input-group">
                                    <input type="text" id="softSkillInput" class="form-control skill-input" placeholder="Enter a soft skill">
                                    <select id="softSkillLevel" class="form-control skill-level">
                                        <option value="Beginner">Beginner</option>
                                        <option value="Intermediate">Intermediate</option>
                                        <option value="Advanced">Advanced</option>
                                        <option value="Expert">Expert</option>
                                    </select>
                                    <button type="button" class="add-item-btn" onclick="addSkill('soft')">Add</button>
                                </div>
                                <div class="skill-tags" id="softSkills"></div>
                            </div>
                        </div>
                        
                        <h3 style="margin-top: 30px; margin-bottom: 15px;">Certifications</h3>
                        <div class="dynamic-list" id="certificationsList">
                            <div class="list-item">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Certification Name</label>
                                        <input type="text" name="certName[]" class="form-control" placeholder="e.g., AWS Certified Solutions Architect">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Issuing Organization</label>
                                        <input type="text" name="certOrg[]" class="form-control" placeholder="e.g., Amazon Web Services">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Issue Date</label>
                                        <input type="month" name="certDate[]" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Expiry Date (if applicable)</label>
                                        <input type="month" name="certExpiry[]" class="form-control">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="add-item-btn" onclick="addCertification()">+ Add Another Certification</button>
                    </div>

                     Section 6: Additional Information 
                    <div class="form-section" data-section="6">
                        <h2>Additional Information</h2>
                        
                        <h3>Languages</h3>
                        <div class="dynamic-list" id="languagesList">
                            <div class="list-item">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Language</label>
                                        <input type="text" name="language[]" class="form-control" placeholder="e.g., English, Spanish">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Proficiency Level</label>
                                        <select name="languageLevel[]" class="form-control">
                                            <option value="Native">Native</option>
                                            <option value="Fluent">Fluent</option>
                                            <option value="Conversational">Conversational</option>
                                            <option value="Basic">Basic</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="add-item-btn" onclick="addLanguage()">+ Add Another Language</button>
                        
                        <h3 style="margin-top: 30px;">Projects (Optional)</h3>
                        <div class="dynamic-list" id="projectsList">
                            <div class="list-item">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Project Name</label>
                                        <input type="text" name="projectName[]" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Project URL</label>
                                        <input type="url" name="projectUrl[]" class="form-control">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Project Description</label>
                                    <textarea name="projectDescription[]" class="form-control" placeholder="Describe the project and your role..."></textarea>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="add-item-btn" onclick="addProject()">+ Add Another Project</button>
                        
                        <div class="form-group" style="margin-top: 30px;">
                            <label for="additionalInfo" class="form-label">Additional Information</label>
                            <textarea id="additionalInfo" name="additionalInfo" class="form-control" placeholder="Any other relevant information, achievements, or notes..."></textarea>
                        </div>
                    </div>

                     Navigation Buttons 
                    <div class="form-navigation">
                        <button type="button" class="nav-btn btn-prev" id="prevBtn" onclick="changeSection(-1)" style="display: none;">Previous</button>
                        <button type="button" class="nav-btn btn-next" id="nextBtn" onclick="changeSection(1)">Next</button>
                        <button type="submit" class="nav-btn btn-submit" id="submitBtn" style="display: none;">Complete Profile</button>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <script>
        let currentSection = 1;
        const totalSections = 6;
        
        // Progress tracking
        function updateProgress() {
            const progress = (currentSection / totalSections) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = `Step ${currentSection} of ${totalSections}: ${getSectionTitle(currentSection)}`;
        }
        
        function getSectionTitle(section) {
            const titles = {
                1: 'Personal Information',
                2: 'Professional Summary',
                3: 'Work Experience',
                4: 'Education',
                5: 'Skills & Certifications',
                6: 'Additional Information'
            };
            return titles[section];
        }
        
        // Section navigation
        function changeSection(direction) {
            const currentSectionEl = document.querySelector(`.form-section[data-section="${currentSection}"]`);
            
            if (direction === 1 && currentSection < totalSections) {
                // Validate current section before proceeding
                if (validateSection(currentSection)) {
                    currentSectionEl.classList.remove('active');
                    currentSection++;
                    document.querySelector(`.form-section[data-section="${currentSection}"]`).classList.add('active');
                }
            } else if (direction === -1 && currentSection > 1) {
                currentSectionEl.classList.remove('active');
                currentSection--;
                document.querySelector(`.form-section[data-section="${currentSection}"]`).classList.add('active');
            }
            
            updateNavigationButtons();
            updateProgress();
        }
        
        function updateNavigationButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const submitBtn = document.getElementById('submitBtn');
            
            prevBtn.style.display = currentSection > 1 ? 'block' : 'none';
            nextBtn.style.display = currentSection < totalSections ? 'block' : 'none';
            submitBtn.style.display = currentSection === totalSections ? 'block' : 'none';
        }
        
        function validateSection(section) {
            const sectionEl = document.querySelector(`.form-section[data-section="${section}"]`);
            const requiredFields = sectionEl.querySelectorAll('[required]');
            
            for (let field of requiredFields) {
                if (!field.value.trim()) {
                    field.focus();
                    alert('Please fill in all required fields before proceeding.');
                    return false;
                }
            }
            return true;
        }
        
        // Dynamic list functions
        function addWorkExperience() {
            const list = document.getElementById('workExperienceList');
            const newItem = document.createElement('div');
            newItem.className = 'list-item';
            newItem.innerHTML = `
                <button type="button" class="remove-item" onclick="removeItem(this)">×</button>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Job Title *</label>
                        <input type="text" name="workTitle[]" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Company *</label>
                        <input type="text" name="workCompany[]" class="form-control" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Start Date</label>
                        <input type="month" name="workStartDate[]" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">End Date</label>
                        <input type="month" name="workEndDate[]" class="form-control">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Job Description</label>
                    <textarea name="workDescription[]" class="form-control" placeholder="Describe your responsibilities and achievements..."></textarea>
                </div>
            `;
            list.appendChild(newItem);
        }
        
        function addEducation() {
            const list = document.getElementById('educationList');
            const newItem = document.createElement('div');
            newItem.className = 'list-item';
            newItem.innerHTML = `
                <button type="button" class="remove-item" onclick="removeItem(this)">×</button>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Degree/Qualification *</label>
                        <input type="text" name="eduDegree[]" class="form-control" placeholder="e.g., Bachelor of Science" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Field of Study</label>
                        <input type="text" name="eduField[]" class="form-control" placeholder="e.g., Computer Science">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Institution *</label>
                        <input type="text" name="eduInstitution[]" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Graduation Year</label>
                        <input type="number" name="eduYear[]" class="form-control" min="1950" max="2030">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">GPA/Grade (Optional)</label>
                    <input type="text" name="eduGrade[]" class="form-control" placeholder="e.g., 3.8/4.0 or First Class">
                </div>
            `;
            list.appendChild(newItem);
        }
        
        function addCertification() {
            const list = document.getElementById('certificationsList');
            const newItem = document.createElement('div');
            newItem.className = 'list-item';
            newItem.innerHTML = `
                <button type="button" class="remove-item" onclick="removeItem(this)">×</button>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Certification Name</label>
                        <input type="text" name="certName[]" class="form-control" placeholder="e.g., AWS Certified Solutions Architect">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Issuing Organization</label>
                        <input type="text" name="certOrg[]" class="form-control" placeholder="e.g., Amazon Web Services">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Issue Date</label>
                        <input type="month" name="certDate[]" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Expiry Date (if applicable)</label>
                        <input type="month" name="certExpiry[]" class="form-control">
                    </div>
                </div>
            `;
            list.appendChild(newItem);
        }
        
        function addLanguage() {
            const list = document.getElementById('languagesList');
            const newItem = document.createElement('div');
            newItem.className = 'list-item';
            newItem.innerHTML = `
                <button type="button" class="remove-item" onclick="removeItem(this)">×</button>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Language</label>
                        <input type="text" name="language[]" class="form-control" placeholder="e.g., English, Spanish">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Proficiency Level</label>
                        <select name="languageLevel[]" class="form-control">
                            <option value="Native">Native</option>
                            <option value="Fluent">Fluent</option>
                            <option value="Conversational">Conversational</option>
                            <option value="Basic">Basic</option>
                        </select>
                    </div>
                </div>
            `;
            list.appendChild(newItem);
        }
        
        function addProject() {
            const list = document.getElementById('projectsList');
            const newItem = document.createElement('div');
            newItem.className = 'list-item';
            newItem.innerHTML = `
                <button type="button" class="remove-item" onclick="removeItem(this)">×</button>
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">Project Name</label>
                        <input type="text" name="projectName[]" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Project URL</label>
                        <input type="url" name="projectUrl[]" class="form-control">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Project Description</label>
                    <textarea name="projectDescription[]" class="form-control" placeholder="Describe the project and your role..."></textarea>
                </div>
            `;
            list.appendChild(newItem);
        }
        
        function removeItem(button) {
            button.parentElement.remove();
        }
        
        // Skills management
        function addSkill(type) {
            const skillInput = document.getElementById(`${type}SkillInput`);
            const skillLevel = document.getElementById(`${type}SkillLevel`);
            const skillsContainer = document.getElementById(`${type}Skills`);
            
            if (skillInput.value.trim()) {
                const skillTag = document.createElement('div');
                skillTag.className = 'skill-tag';
                skillTag.innerHTML = `
                    ${skillInput.value} (${skillLevel.value})
                    <button type="button" class="remove-skill" onclick="removeSkill(this)">×</button>
                    <input type="hidden" name="${type}Skills[]" value="${skillInput.value}|${skillLevel.value}">
                `;
                skillsContainer.appendChild(skillTag);
                skillInput.value = '';
            }
        }
        
        function removeSkill(button) {
            button.parentElement.remove();
        }
        
        // Form submission
        document.getElementById('profileForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Collect all form data
            const formData = new FormData(this);
            const profileData = {};
            
            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                if (profileData[key]) {
                    if (Array.isArray(profileData[key])) {
                        profileData[key].push(value);
                    } else {
                        profileData[key] = [profileData[key], value];
                    }
                } else {
                    profileData[key] = value;
                }
            }
            
            // Save to localStorage (in a real app, this would be sent to a server)
            localStorage.setItem('userProfile', JSON.stringify(profileData));
            
            alert('Profile completed successfully! Your information has been saved.');
            
            // Redirect to profile view page
            window.location.href = 'user-profile-view.html';
        });
        
        // Initialize
        updateProgress();
        updateNavigationButtons();
        
        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && e.target.type !== 'textarea') {
                e.preventDefault();
                if (currentSection < totalSections) {
                    changeSection(1);
                }
            }
        });
    </script>
</body>
</html>