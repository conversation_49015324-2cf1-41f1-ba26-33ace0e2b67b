<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Psychometric Questionnaire - Step 3 | Career Scopes</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f8f7;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .questionnaire-container {
            background-color: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            padding: 40px;
            position: relative;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 400;
            font-style: italic;
        }
        
        .step-indicator {
            color: #5acfbc;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .services-grid {
            display: grid;
            gap: 15px;
            margin-bottom: 40px;
        }
        
        .service-option {
            position: relative;
        }
        
        .service-option input[type="checkbox"] {
            display: none;
        }
        
        .service-card {
            display: block;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            background-color: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .service-card:hover {
            border-color: #5acfbc;
            background-color: #f0f8f7;
        }
        
        .service-option input[type="checkbox"]:checked + .service-card {
            border-color: #5acfbc;
            background-color: #e8f5f3;
        }
        
        .service-title {
            color: #333;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .service-description {
            color: #666;
            font-size: 0.85rem;
            line-height: 1.4;
        }
        
        .service-icon {
            font-size: 1.5rem;
            margin-bottom: 10px;
            display: block;
        }
        
        .selected-services {
            background-color: #f0f8f7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            display: none;
        }
        
        .selected-services.show {
            display: block;
        }
        
        .selected-title {
            color: #333;
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .selected-list {
            list-style: none;
        }
        
        .selected-item {
            color: #5acfbc;
            font-size: 0.9rem;
            margin-bottom: 5px;
            padding-left: 20px;
            position: relative;
        }
        
        .selected-item::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #5acfbc;
            font-weight: bold;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            padding-top: 20px;
        }
        
        .nav-btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .btn-back {
            background-color: transparent;
            color: #5acfbc;
            border: 2px solid #5acfbc;
        }
        
        .btn-back:hover {
            background-color: #5acfbc;
            color: white;
        }
        
        .btn-submit {
            background-color: #5acfbc;
            color: white;
            border: 2px solid #5acfbc;
        }
        
        .btn-submit:hover {
            background-color: #48b0a0;
            border-color: #48b0a0;
        }
        
        .btn-submit:disabled {
            background-color: #ccc;
            border-color: #ccc;
            cursor: not-allowed;
        }
        
        @media (max-width: 768px) {
            .questionnaire-container {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="questionnaire-container">
        <div class="header">
            <h1>Psychometric Questionnaire</h1>
            <div class="step-indicator">Step 3 of 3 - Select Your Services</div>
        </div>
        
        <form id="step3Form">
            <div class="services-grid">
                <div class="service-option">
                    <input type="checkbox" id="career-coaching" name="services" value="career-coaching">
                    <label for="career-coaching" class="service-card">
                        <span class="service-icon">🎯</span>
                        <div class="service-title">Career Coaching</div>
                        <div class="service-description">One-on-one guidance to help you navigate your career path and achieve your professional goals</div>
                    </label>
                </div>
                
                <div class="service-option">
                    <input type="checkbox" id="interview-optimization" name="services" value="interview-optimization">
                    <label for="interview-optimization" class="service-card">
                        <span class="service-icon">💼</span>
                        <div class="service-title">Interview Optimization</div>
                        <div class="service-description">Comprehensive interview preparation and practice sessions to boost your confidence</div>
                    </label>
                </div>
                
                <div class="service-option">
                    <input type="checkbox" id="resume-cover-letter" name="services" value="resume-cover-letter">
                    <label for="resume-cover-letter" class="service-card">
                        <span class="service-icon">📝</span>
                        <div class="service-title">Resume & Cover Letter</div>
                        <div class="service-description">Professional resume and cover letter writing services tailored to your industry</div>
                    </label>
                </div>
                
                <div class="service-option">
                    <input type="checkbox" id="networking-guidance" name="services" value="networking-guidance">
                    <label for="networking-guidance" class="service-card">
                        <span class="service-icon">🤝</span>
                        <div class="service-title">Networking Guidance</div>
                        <div class="service-description">Learn effective networking strategies to build meaningful professional relationships</div>
                    </label>
                </div>
                
                <div class="service-option">
                    <input type="checkbox" id="interview-coaching" name="services" value="interview-coaching">
                    <label for="interview-coaching" class="service-card">
                        <span class="service-icon">🗣️</span>
                        <div class="service-title">Interview Coaching</div>
                        <div class="service-description">Personalized coaching sessions to master interview techniques and communication skills</div>
                    </label>
                </div>
                
                <div class="service-option">
                    <input type="checkbox" id="psychometric-feedback" name="services" value="psychometric-feedback">
                    <label for="psychometric-feedback" class="service-card">
                        <span class="service-icon">📊</span>
                        <div class="service-title">Psychometric Analysis Feedback</div>
                        <div class="service-description">Detailed analysis of your assessment results with actionable insights and recommendations</div>
                    </label>
                </div>
            </div>
            
            <div class="selected-services" id="selectedServices">
                <div class="selected-title">Selected Services:</div>
                <ul class="selected-list" id="selectedList"></ul>
            </div>
            
            <div class="navigation">
                <button type="button" class="nav-btn btn-back" onclick="goBack()">Back</button>
                <button type="submit" class="nav-btn btn-submit" id="submitBtn">Submit</button>
            </div>
        </form>
    </div>
    
    <script>
        const serviceNames = {
            'career-coaching': 'Career Coaching',
            'interview-optimization': 'Interview Optimization',
            'resume-cover-letter': 'Resume & Cover Letter',
            'networking-guidance': 'Networking Guidance',
            'interview-coaching': 'Interview Coaching',
            'psychometric-feedback': 'Psychometric Analysis Feedback'
        };
        
        function updateSelectedServices() {
            const checkboxes = document.querySelectorAll('input[name="services"]:checked');
            const selectedServices = document.getElementById('selectedServices');
            const selectedList = document.getElementById('selectedList');
            const submitBtn = document.getElementById('submitBtn');
            
            if (checkboxes.length > 0) {
                selectedServices.classList.add('show');
                selectedList.innerHTML = '';
                
                checkboxes.forEach(checkbox => {
                    const li = document.createElement('li');
                    li.className = 'selected-item';
                    li.textContent = serviceNames[checkbox.value];
                    selectedList.appendChild(li);
                });
                
                submitBtn.disabled = false;
            } else {
                selectedServices.classList.remove('show');
                submitBtn.disabled = true;
            }
        }
        
        // Add event listeners to all checkboxes
        document.querySelectorAll('input[name="services"]').forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedServices);
        });
        
        function goBack() {
            window.location.href = 'psychometric-step2.html';
        }
        
        document.getElementById('step3Form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Collect selected services
            const selectedServices = Array.from(document.querySelectorAll('input[name="services"]:checked'))
                .map(checkbox => checkbox.value);
            
            if (selectedServices.length === 0) {
                alert('Please select at least one service to continue.');
                return;
            }
            
            // Save to localStorage
            localStorage.setItem('psychometricStep3', JSON.stringify({ services: selectedServices }));
            
            // Collect all data from previous steps
            const step1Data = JSON.parse(localStorage.getItem('psychometricStep1') || '{}');
            const step2Data = JSON.parse(localStorage.getItem('psychometricStep2') || '{}');
            const step3Data = { services: selectedServices };
            
            const completeAssessment = {
                ...step1Data,
                ...step2Data,
                ...step3Data,
                completedAt: new Date().toISOString()
            };
            
            // Save complete assessment
            localStorage.setItem('psychometricAssessment', JSON.stringify(completeAssessment));
            
            // Show success message and redirect
            alert('Assessment completed successfully! Thank you for your responses. We will contact you soon with your personalized career recommendations.');
            
            // In a real application, this would redirect to a results page or dashboard
            window.location.href = 'index.html';
        });
        
        // Load saved data if returning to this step
        window.addEventListener('load', function() {
            const savedData = localStorage.getItem('psychometricStep3');
            if (savedData) {
                const data = JSON.parse(savedData);
                
                if (data.services) {
                    data.services.forEach(service => {
                        const checkbox = document.querySelector(`input[value="${service}"]`);
                        if (checkbox) {
                            checkbox.checked = true;
                        }
                    });
                    updateSelectedServices();
                }
            }
            
            // Initial state
            updateSelectedServices();
        });
    </script>
</body>
</html>
