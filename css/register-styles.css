/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

/* Registration Container */
.register-container {
  width: 100%;
  max-width: 550px;
  /* padding: 40px 20px; */
}

/* Logo */
.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
}

.logo img {
  width: 150px;
  height: 150px;
}

.logo-letter {
  font-size: 60px;
  font-weight: 700;
  color: #7fb9b1;
  line-height: 1;
}

.logo-text {
  text-align: center;
  color: #7fb9b1;
}

.logo-name {
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 2px;
}

.logo-tagline {
  font-size: 10px;
  letter-spacing: 1px;
  text-transform: uppercase;
}

/* Heading */
h1 {
  font-size: 28px;
  font-weight: 500;
  color: #7fb9b1;
  text-align: center;
  margin-bottom: 40px;
}

/* Form */
.register-form {
  width: 100%;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #555;
}

input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
select {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  color: #333;
  transition: border-color 0.3s;
}

input:focus,
select:focus {
  outline: none;
  border-color: #7fb9b1;
}

/* Select Styling */
.select-wrapper {
  position: relative;
}

.select-wrapper::after {
  content: "▼";
  font-size: 12px;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #777;
  pointer-events: none;
}

select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-color: #fff;
  cursor: pointer;
}

/* Checkbox Styling */
.checkbox-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.checkbox-group label {
  margin-bottom: 0;
  font-size: 14px;
}

input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #7fb9b1;
  cursor: pointer;
}

/* Links */
a {
  color: #7fb9b1;
  text-decoration: none;
  transition: color 0.3s;
}

a:hover {
  color: #5a9b92;
  text-decoration: underline;
}

/* Register Button */
.register-btn {
  width: 100%;
  padding: 14px;
  background-color: #7fb9b1;
  color: #fff;
  border: none;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 10px;
}

.register-btn:hover {
  background-color: #5a9b92;
}

/* Responsive Design */
@media (max-width: 576px) {
  .register-container {
    padding: 20px 15px;
  }

  h1 {
    font-size: 24px;
    margin-bottom: 30px;
  }

  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="password"],
  select {
    padding: 10px 12px;
    font-size: 14px;
  }

  .register-btn {
    padding: 12px;
  }
}
