/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #e6f4f1;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

a {
  text-decoration: none;
  color: inherit;
}

ul {
  list-style: none;
}

/* Header */
header {
  padding: 20px 0;
  background-color: #e6f4f1;
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  width: 150px;
  height: 150px;
}

.logo-letter {
  font-size: 32px;
  font-weight: 700;
  color: #7fb9b1;
  margin-right: 5px;
}

.logo-text {
  font-size: 12px;
  font-weight: 600;
  color: #7fb9b1;
  letter-spacing: 1px;
}

nav ul {
  display: flex;
}

nav ul li {
  margin-left: 30px;
}

nav ul li a {
  font-weight: 500;
  position: relative;
}

nav ul li a.active::after,
nav ul li a:hover::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #000;
}

.auth-buttons {
  display: flex;
  gap: 10px;
}

.logout-btn {
  padding: 8px 20px;
  border-radius: 20px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  background-color: #f0f0f0;
  color: #333;
}

/* Main Content */
main {
  padding: 20px 0 60px;
}

.welcome-section {
  margin-bottom: 30px;
}

.welcome-section h1 {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 10px;
}

.welcome-section p {
  font-size: 18px;
  color: #444;
}

/* Profile Grid */
.profile-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 40px;
}

.profile-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.card-icon {
  color: #333;
}

.card-content h2 {
  font-size: 20px;
  font-weight: 600;
}

.add-btn {
  background-color: #b8e0d2;
  color: #333;
  border: none;
  border-radius: 20px;
  padding: 8px 25px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-btn:hover {
  background-color: #9ed4c2;
}

/* Practitioner Section */
.practitioner-section {
  background-color: #b8e0d2;
  border-radius: 10px;
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 30px;
}

.practitioner-image {
  width: 100px;
  height: 100px;
  background-color: #fff;
  border-radius: 5px;
  overflow: hidden;
}

.practitioner-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.practitioner-info {
  flex: 1;
}

.practitioner-info h2 {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 5px;
}

.practitioner-info p {
  margin-bottom: 15px;
  font-size: 16px;
}

.contact-btn {
  background-color: #8c8c8c;
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 8px 25px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.contact-btn:hover {
  background-color: #777;
}

/* Footer */
footer {
  background-color: #7fb9b1;
  color: #fff;
  padding: 60px 0 20px;
}

.footer-top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40px;
}

.footer-info {
  max-width: 400px;
}

.footer-info h3 {
  font-size: 24px;
  margin-bottom: 20px;
}

.footer-info p {
  margin-bottom: 10px;
  font-size: 14px;
}

.footer-links {
  display: flex;
  gap: 60px;
}

.footer-column h4 {
  margin-bottom: 20px;
  font-size: 18px;
}

.footer-column ul li {
  margin-bottom: 10px;
}

.social-icons {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.social-icon {
  color: #fff;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.flags {
  display: flex;
  gap: 10px;
}

.flag {
  width: 40px;
  height: 25px;
  object-fit: cover;
}

.copyright {
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 992px) {
  .profile-grid {
    grid-template-columns: 1fr;
  }

  .footer-top {
    flex-direction: column;
    gap: 40px;
  }

  .footer-info {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  header .container {
    flex-direction: column;
    gap: 20px;
  }

  nav ul {
    margin-top: 20px;
  }

  .practitioner-section {
    flex-direction: column;
    text-align: center;
  }

  .footer-links {
    flex-direction: column;
    gap: 30px;
  }
}

@media (max-width: 576px) {
  nav ul {
    flex-wrap: wrap;
    justify-content: center;
  }

  nav ul li {
    margin: 0 10px 10px;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .flags {
    justify-content: center;
  }
}
