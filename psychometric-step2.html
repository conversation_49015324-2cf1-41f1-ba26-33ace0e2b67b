<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Psychometric Questionnaire - Step 2 | Career Scopes</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f8f7;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .questionnaire-container {
            background-color: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            padding: 40px;
            position: relative;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 400;
            font-style: italic;
        }
        
        .step-indicator {
            color: #5acfbc;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .upload-section {
            margin-bottom: 30px;
        }
        
        .upload-area {
            border: 2px dashed #d1ecf1;
            border-radius: 15px;
            background-color: #f8f9fa;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .upload-area:hover {
            border-color: #5acfbc;
            background-color: #f0f8f7;
        }
        
        .upload-area.dragover {
            border-color: #5acfbc;
            background-color: #e8f5f3;
        }
        
        .upload-icon {
            font-size: 2rem;
            color: #5acfbc;
            margin-bottom: 15px;
        }
        
        .upload-text {
            color: #666;
            font-size: 1rem;
            margin-bottom: 10px;
        }
        
        .upload-subtext {
            color: #999;
            font-size: 0.8rem;
        }
        
        .file-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }
        
        .file-list {
            margin-top: 15px;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 15px;
            background-color: #e8f5f3;
            border-radius: 8px;
            margin-bottom: 8px;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .file-icon {
            color: #5acfbc;
        }
        
        .file-name {
            color: #333;
            font-size: 0.9rem;
        }
        
        .file-size {
            color: #666;
            font-size: 0.8rem;
        }
        
        .remove-file {
            background: none;
            border: none;
            color: #dc3545;
            cursor: pointer;
            font-size: 1.2rem;
            padding: 5px;
        }
        
        .remove-file:hover {
            color: #c82333;
        }
        
        .navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            padding-top: 20px;
        }
        
        .nav-btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .btn-back {
            background-color: transparent;
            color: #5acfbc;
            border: 2px solid #5acfbc;
        }
        
        .btn-back:hover {
            background-color: #5acfbc;
            color: white;
        }
        
        .btn-next {
            background-color: #5acfbc;
            color: white;
            border: 2px solid #5acfbc;
        }
        
        .btn-next:hover {
            background-color: #48b0a0;
            border-color: #48b0a0;
        }
        
        @media (max-width: 768px) {
            .questionnaire-container {
                padding: 30px 20px;
            }
            
            .upload-area {
                padding: 30px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="questionnaire-container">
        <div class="header">
            <h1>Psychometric Questionnaire</h1>
            <div class="step-indicator">Step 2 of 3 - Upload Documents</div>
        </div>
        
        <form id="step2Form">
            <div class="upload-section">
                <div class="upload-area" onclick="document.getElementById('resumeInput').click()">
                    <div class="upload-icon">📄</div>
                    <div class="upload-text">Upload Resume / CV</div>
                    <div class="upload-subtext">Click to browse or drag and drop</div>
                    <input type="file" id="resumeInput" class="file-input" accept=".pdf,.doc,.docx" onchange="handleFileUpload(this, 'resume')">
                </div>
                <div class="file-list" id="resumeFiles"></div>
            </div>
            
            <div class="upload-section">
                <div class="upload-area" onclick="document.getElementById('certificatesInput').click()">
                    <div class="upload-icon">🏆</div>
                    <div class="upload-text">Upload Certificates / Portfolio</div>
                    <div class="upload-subtext">Click to browse or drag and drop</div>
                    <input type="file" id="certificatesInput" class="file-input" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" multiple onchange="handleFileUpload(this, 'certificates')">
                </div>
                <div class="file-list" id="certificatesFiles"></div>
            </div>
            
            <div class="upload-section">
                <div class="upload-area" onclick="document.getElementById('extrasInput').click()">
                    <div class="upload-icon">📎</div>
                    <div class="upload-text">Upload Optional Extras</div>
                    <div class="upload-subtext">Click to browse or drag and drop</div>
                    <input type="file" id="extrasInput" class="file-input" accept="*" multiple onchange="handleFileUpload(this, 'extras')">
                </div>
                <div class="file-list" id="extrasFiles"></div>
            </div>
            
            <div class="navigation">
                <button type="button" class="nav-btn btn-back" onclick="goBack()">Back</button>
                <button type="submit" class="nav-btn btn-next">Next</button>
            </div>
        </form>
    </div>
    
    <script>
        let uploadedFiles = {
            resume: [],
            certificates: [],
            extras: []
        };
        
        function handleFileUpload(input, category) {
            const files = Array.from(input.files);
            
            files.forEach(file => {
                if (category === 'resume') {
                    // Only allow one resume
                    uploadedFiles.resume = [file];
                } else {
                    uploadedFiles[category].push(file);
                }
            });
            
            displayFiles(category);
            input.value = ''; // Reset input
        }
        
        function displayFiles(category) {
            const container = document.getElementById(category + 'Files');
            container.innerHTML = '';
            
            uploadedFiles[category].forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <span class="file-icon">${getFileIcon(file.type)}</span>
                        <div>
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${formatFileSize(file.size)}</div>
                        </div>
                    </div>
                    <button type="button" class="remove-file" onclick="removeFile('${category}', ${index})">×</button>
                `;
                container.appendChild(fileItem);
            });
        }
        
        function removeFile(category, index) {
            uploadedFiles[category].splice(index, 1);
            displayFiles(category);
        }
        
        function getFileIcon(fileType) {
            if (fileType.includes('pdf')) return '📄';
            if (fileType.includes('doc')) return '📝';
            if (fileType.includes('image')) return '🖼️';
            return '📎';
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function goBack() {
            window.location.href = 'psychometric-step1.html';
        }
        
        // Drag and drop functionality
        document.querySelectorAll('.upload-area').forEach(area => {
            area.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });
            
            area.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });
            
            area.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                
                const files = Array.from(e.dataTransfer.files);
                const category = this.querySelector('input').id.replace('Input', '');
                
                files.forEach(file => {
                    if (category === 'resume') {
                        uploadedFiles.resume = [file];
                    } else {
                        uploadedFiles[category].push(file);
                    }
                });
                
                displayFiles(category);
            });
        });
        
        document.getElementById('step2Form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Save file information to localStorage (in a real app, files would be uploaded to server)
            const fileData = {
                resume: uploadedFiles.resume.map(f => ({ name: f.name, size: f.size, type: f.type })),
                certificates: uploadedFiles.certificates.map(f => ({ name: f.name, size: f.size, type: f.type })),
                extras: uploadedFiles.extras.map(f => ({ name: f.name, size: f.size, type: f.type }))
            };
            
            localStorage.setItem('psychometricStep2', JSON.stringify(fileData));
            
            // Navigate to step 3
            window.location.href = 'psychometric-step3.html';
        });
        
        // Load saved data if returning to this step
        window.addEventListener('load', function() {
            const savedData = localStorage.getItem('psychometricStep2');
            if (savedData) {
                const data = JSON.parse(savedData);
                // Note: In a real application, you would need to handle file restoration differently
                // This is just for demonstration of the UI state
            }
        });
    </script>
</body>
</html>
