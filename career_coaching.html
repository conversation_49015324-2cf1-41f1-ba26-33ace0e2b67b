<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Career Coaching - CareerScopes</title>
    <link rel="stylesheet" href="./css/styles.css" />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
    />
    <style>
      .header {
        padding: 20px 0;
        background-color: #fff;
      }

      .header .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      /* Career Coaching Specific Styles */
      .career-hero {
        background: linear-gradient(135deg, #7fb9b1 0%, #a8d5d1 100%);
        padding: 80px 0;
        border-radius: 20px;
        margin: 20px;
        position: relative;
        overflow: hidden;
      }

      .career-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url('./images/Foreground.jpeg');
        background-size: cover;
        background-position: center;
        opacity: 0.3;
        z-index: 1;
      }

      .career-hero .container {
        position: relative;
        z-index: 2;
        text-align: center;
      }

      .career-hero h1 {
        color: white;
        font-size: 48px;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      }

      .main-content {
        padding: 60px 0;
        text-align: center;
      }

      .main-title {
        font-size: 36px;
        font-weight: 700;
        color: #333;
        margin-bottom: 20px;
        line-height: 1.2;
      }

      .book-btn {
        background-color: #00d4aa;
        color: white;
        padding: 15px 30px;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        margin: 30px 0;
        transition: background-color 0.3s;
      }

      .book-btn:hover {
        background-color: #00b894;
      }

      .what-we-do {
        padding: 40px 0;
      }

      .what-we-do h2 {
        font-size: 32px;
        font-weight: 700;
        color: #333;
        margin-bottom: 20px;
      }

      .what-we-do p {
        font-size: 18px;
        color: #666;
        max-width: 800px;
        margin: 0 auto;
        line-height: 1.6;
      }

      .contact-form-section {
        background-color: #f8f9fa;
        padding: 60px 0;
        margin: 40px 0;
      }

      .contact-form {
        max-width: 600px;
        margin: 0 auto;
        background: white;
        padding: 40px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      }

      .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
      }

      .form-group {
        flex: 1;
      }

      .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #333;
      }

      .form-group input,
      .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 16px;
        font-family: inherit;
      }

      .form-group textarea {
        height: 120px;
        resize: vertical;
      }

      .submit-btn {
        background-color: #00d4aa;
        color: white;
        padding: 15px 40px;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        width: 100%;
        margin-top: 20px;
        transition: background-color 0.3s;
      }

      .submit-btn:hover {
        background-color: #00b894;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .header .container {
          flex-direction: column;
          gap: 20px;
        }

        .career-hero h1 {
          font-size: 36px;
        }

        .main-title {
          font-size: 28px;
        }

        .form-row {
          flex-direction: column;
          gap: 0;
        }

        .contact-form {
          margin: 0 20px;
          padding: 30px 20px;
        }

        .career-hero {
          margin: 10px;
          padding: 60px 0;
        }
      }

      @media (max-width: 576px) {
        .career-hero h1 {
          font-size: 28px;
        }

        .main-title {
          font-size: 24px;
        }

        .what-we-do p {
          font-size: 16px;
        }
      }
    </style>
  </head>
  <body>
    <!-- Header Section -->
    <div class="header">
      <div class="container">
        <div class="logo">
          <a href="./index.html">
            <img src="./images/logo.jpg" alt="careerScops" />
          </a>
        </div>
        <nav>
          <ul>
            <li><a href="./index.html">Home</a></li>
            <li><a href="./about-us.html">About us</a></li>
            <li><a href="./career_coaching.html" class="active">Services</a></li>
            <li><a href="./testimonials.html">Testimonials</a></li>
          </ul>
        </nav>
        <div class="auth-buttons">
          <a href="./login.html" class="login-btn">Login</a>
          <a href="./register.html" class="register-btn">Register</a>
        </div>
      </div>
    </div>

    <!-- Career Coaching Hero Section -->
    <section class="career-hero">
      <div class="container">
        <h1>Career Coaching</h1>
      </div>
    </section>

    <!-- Main Content Section -->
    <section class="main-content">
      <div class="container">
        <h2 class="main-title">Transform Career with your<br>personalised Guide</h2>
        <button class="book-btn">Book Consultation</button>
        
        <div class="what-we-do">
          <h2>What We Do ?</h2>
          <p>
            Get clarity on your career goals, develop actionable plans, and 
            overcome professional challenges with one-on-one guidance from an 
            experienced career coach.
          </p>
        </div>
      </div>
    </section>

    <!-- Contact Form Section -->
    <section class="contact-form-section">
      <div class="container">
        <form class="contact-form">
          <div class="form-row">
            <div class="form-group">
              <label for="firstName">First Name</label>
              <input type="text" id="firstName" name="firstName" required>
            </div>
            <div class="form-group">
              <label for="lastName">Last Name</label>
              <input type="text" id="lastName" name="lastName" required>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="email">Email</label>
              <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
              <label for="phone">Phone Number</label>
              <input type="tel" id="phone" name="phone" required>
            </div>
          </div>
          
          <div class="form-group">
            <label for="message">Message</label>
            <textarea id="message" name="message" placeholder="Write your message..." required></textarea>
          </div>
          
          <button type="submit" class="submit-btn">Send Message</button>
        </form>
      </div>
    </section>

    <!-- Footer Section -->
    <footer>
      <div class="container">
        <div class="footer-top">
          <div class="footer-info">
            <h3>CareerScopes</h3>
            <p>
              Career Scopes would like to acknowledge the traditional custodians
              of the land on which we are based, the Aboriginal people of the
              Kulin Nation.
            </p>
            <p>
              We also pay our respects to their Elders, past, present and
              future, whose cultures and customs have nurtured and continue to
              nurture this land.
            </p>
            <p>We pay our respects to Elders past, present and future.</p>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h4>About</h4>
              <ul>
                <li><a href="#">JOBS</a></li>
                <li><a href="#">FAQS</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h4>Legal</h4>
              <ul>
                <li><a href="#">Terms and Conditions</a></li>
                <li><a href="#">Privacy Policy</a></li>
                <li><a href="#">Cookie Policy</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h4>Contact us</h4>
              <div class="social-icons">
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                    ></path>
                  </svg>
                  <span>0421 594 877</span>
                </a>
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                    ></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                  <span style="font-size: 0.75rem"
                    ><EMAIL></span
                  >
                </a>
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"
                    ></path>
                    <rect x="2" y="9" width="4" height="12"></rect>
                    <circle cx="4" cy="4" r="2"></circle>
                  </svg>
                  <span style="font-size: 1rem"
                    >www.linkedin.com/in/lijesla</span
                  >
                </a>
              </div>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <div class="flags">
            <img
              src="./images/image 13.png"
              alt="Aboriginal Flag"
              class="flag"
            />
            <img
              src="./images/image 14.png"
              alt="Torres Strait Islander Flag"
              class="flag"
            />
          </div>
          <div class="copyright">
            © CareerScope - All Rights Reserved. Love From Australia
          </div>
        </div>
      </div>
    </footer>
  </body>
</html>
