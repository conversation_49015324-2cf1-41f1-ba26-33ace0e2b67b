<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Psychometric Questionnaire - Step 1 | Career Scopes</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f8f7;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .questionnaire-container {
            background-color: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            padding: 40px;
            position: relative;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 400;
            font-style: italic;
        }

        .step-indicator {
            color: #5acfbc;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .section-number {
            color: #5acfbc;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .section-title {
            color: #333;
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 15px;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        }

        .form-control:focus {
            outline: none;
            border-color: #5acfbc;
            background-color: white;
        }

        .radio-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .radio-option {
            position: relative;
        }

        .radio-option input[type="radio"] {
            display: none;
        }

        .radio-option label {
            display: block;
            padding: 8px 20px;
            border: 2px solid #e9ecef;
            border-radius: 20px;
            background-color: #f8f9fa;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .radio-option input[type="radio"]:checked+label {
            border-color: #5acfbc;
            background-color: #5acfbc;
            color: white;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .checkbox-option {
            position: relative;
        }

        .checkbox-option input[type="checkbox"] {
            display: none;
        }

        .checkbox-option label {
            display: block;
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 20px;
            background-color: #f8f9fa;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            text-align: center;
        }

        .checkbox-option input[type="checkbox"]:checked+label {
            border-color: #5acfbc;
            background-color: #5acfbc;
            color: white;
        }

        textarea.form-control {
            min-height: 100px;
            resize: vertical;
            border-radius: 15px;
        }

        .navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            padding-top: 20px;
        }

        .nav-btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-back {
            background-color: transparent;
            color: #5acfbc;
            border: 2px solid #5acfbc;
        }

        .btn-back:hover {
            background-color: #5acfbc;
            color: white;
        }

        .btn-next {
            background-color: #5acfbc;
            color: white;
            border: 2px solid #5acfbc;
        }

        .btn-next:hover {
            background-color: #48b0a0;
            border-color: #48b0a0;
        }

        @media (max-width: 768px) {
            .questionnaire-container {
                padding: 30px 20px;
            }

            .checkbox-group {
                grid-template-columns: 1fr;
            }

            .radio-group {
                flex-direction: column;
            }
        }
    </style>
</head>

<body>
    <div class="questionnaire-container">
        <div class="header">
            <h1>Psychometric Questionnaire</h1>
            <div class="step-indicator">Step 1 of 3 - Career</div>
        </div>

        <form id="step1Form">
            <div class="form-section">
                <div class="section-number">1. Career Interest</div>
                <div class="section-title">What field interests you most?</div>
                <select style="appearance: none;" class="form-control" name="careerInterest" required>
                    <option value="">Select field</option>
                    <option value="healthcare">Healthcare</option>
                    <option value="technology">Technology</option>
                    <option value="finance">Finance</option>
                    <option value="education">Education</option>
                    <option value="marketing">Marketing</option>
                    <option value="engineering">Engineering</option>
                    <option value="arts">Arts & Creative</option>
                    <option value="business">Business</option>
                    <option value="science">Science</option>
                    <option value="other">Other</option>
                </select>
            </div>

            <div class="form-section">
                <div class="section-number">2. Skills Confidence</div>
                <div class="section-title">How confident are you in your current skills?</div>
                <div class="radio-group">
                    <div class="radio-option">
                        <input type="radio" id="confidence-low" name="skillsConfidence" value="low" required>
                        <label for="confidence-low">Low</label>
                    </div>
                    <div class="radio-option">
                        <input type="radio" id="confidence-medium" name="skillsConfidence" value="medium">
                        <label for="confidence-medium">Medium</label>
                    </div>
                    <div class="radio-option">
                        <input type="radio" id="confidence-high" name="skillsConfidence" value="high">
                        <label for="confidence-high">High</label>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <div class="section-number">3. Work Environment Preference</div>
                <div class="section-title">What work environment do you prefer?</div>
                <div class="radio-group">
                    <div class="radio-option">
                        <input type="radio" id="env-remote" name="workEnvironment" value="remote" required>
                        <label for="env-remote">Remote</label>
                    </div>
                    <div class="radio-option">
                        <input type="radio" id="env-hybrid" name="workEnvironment" value="hybrid">
                        <label for="env-hybrid">Hybrid</label>
                    </div>
                    <div class="radio-option">
                        <input type="radio" id="env-office" name="workEnvironment" value="office">
                        <label for="env-office">Office</label>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <div class="section-number">4. Personality</div>
                <div class="section-title">Which best describes your personality?</div>
                <div class="radio-group">
                    <div class="radio-option">
                        <input type="radio" id="personality-introvert" name="personality" value="introvert" required>
                        <label for="personality-introvert">Introvert</label>
                    </div>
                    <div class="radio-option">
                        <input type="radio" id="personality-ambivert" name="personality" value="ambivert">
                        <label for="personality-ambivert">Ambivert</label>
                    </div>
                    <div class="radio-option">
                        <input type="radio" id="personality-extrovert" name="personality" value="extrovert">
                        <label for="personality-extrovert">Extrovert</label>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <div class="section-number">5. Career Values & Goals</div>
                <div class="section-title">What matters most to you in your career? (Select all that apply)</div>
                <div class="checkbox-group">
                    <div class="checkbox-option">
                        <input type="checkbox" id="value-salary" name="careerValues" value="high-salary">
                        <label for="value-salary">High Salary</label>
                    </div>
                    <div class="checkbox-option">
                        <input type="checkbox" id="value-balance" name="careerValues" value="work-life-balance">
                        <label for="value-balance">Work-Life Balance</label>
                    </div>
                    <div class="checkbox-option">
                        <input type="checkbox" id="value-growth" name="careerValues" value="career-growth">
                        <label for="value-growth">Career Growth</label>
                    </div>
                    <div class="checkbox-option">
                        <input type="checkbox" id="value-creativity" name="careerValues" value="creativity">
                        <label for="value-creativity">Creativity</label>
                    </div>
                    <div class="checkbox-option">
                        <input type="checkbox" id="value-stability" name="careerValues" value="job-security">
                        <label for="value-stability">Job Security</label>
                    </div>
                    <div class="checkbox-option">
                        <input type="checkbox" id="value-impact" name="careerValues" value="social-impact">
                        <label for="value-impact">Social Impact</label>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <div class="section-number">6. Tell us about yourself</div>
                <div class="section-title">Share any additional information about your career goals</div>
                <textarea class="form-control" name="additionalInfo"
                    placeholder="Tell us more about your aspirations, challenges, or specific goals..."></textarea>
            </div>

            <div class="navigation">
                <button type="button" class="nav-btn btn-back" onclick="goBack()">Back</button>
                <button type="submit" class="nav-btn btn-next">Next</button>
            </div>
        </form>
    </div>

    <script>
        function goBack() {
            // In a real application, this would navigate to the previous page
            window.history.back();
        }

        document.getElementById('step1Form').addEventListener('submit', function (e) {
            e.preventDefault();

            // Collect form data
            const formData = new FormData(this);
            const step1Data = {};

            // Handle regular form fields
            for (let [key, value] of formData.entries()) {
                if (step1Data[key]) {
                    if (Array.isArray(step1Data[key])) {
                        step1Data[key].push(value);
                    } else {
                        step1Data[key] = [step1Data[key], value];
                    }
                } else {
                    step1Data[key] = value;
                }
            }

            // Save to localStorage
            localStorage.setItem('psychometricStep1', JSON.stringify(step1Data));

            // Navigate to step 2
            window.location.href = 'psychometric-step2.html';
        });

        // Load saved data if returning to this step
        window.addEventListener('load', function () {
            const savedData = localStorage.getItem('psychometricStep1');
            if (savedData) {
                const data = JSON.parse(savedData);

                // Populate form fields
                Object.keys(data).forEach(key => {
                    const element = document.querySelector(`[name="${key}"]`);
                    if (element) {
                        if (element.type === 'radio' || element.type === 'checkbox') {
                            if (Array.isArray(data[key])) {
                                data[key].forEach(value => {
                                    const option = document.querySelector(`[name="${key}"][value="${value}"]`);
                                    if (option) option.checked = true;
                                });
                            } else {
                                const option = document.querySelector(`[name="${key}"][value="${data[key]}"]`);
                                if (option) option.checked = true;
                            }
                        } else {
                            element.value = data[key];
                        }
                    }
                });
            }
        });
    </script>
</body>

</html>