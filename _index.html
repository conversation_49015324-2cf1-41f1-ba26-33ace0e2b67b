<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Career Scopes - Professional Practitioner</title>
    <style>
      /* Reset and base styles */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f8f9fa;
      }

      /* Container */
      .container {
        width: 90%;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      /* Header */
      header {
        background-color: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        position: fixed;
        width: 100%;
        top: 0;
        z-index: 100;
      }

      nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
      }

      .logo {
        display: flex;
        align-items: center;
        /* box-shadow: 0px 0px 10px 0px red;
        border: 2px solid blue;
        outline: 2px solid green;
        outline-offset: 2px; */
      }

      .logo img {
        height: 60px;
        width: auto;
      }

      .nav-links {
        display: flex;
        list-style: none;
        align-items: center;
      }

      .nav-links li {
        margin-left: 30px;
      }

      .nav-links a {
        text-decoration: none;
        color: #333;
        font-weight: 500;
        transition: color 0.3s;
      }

      .nav-links a:hover {
        color: #5acfbc;
      }

      .auth-buttons {
        display: flex;
        gap: 10px;
        margin-left: 30px;
      }

      .btn-login {
        padding: 8px 20px;
        border: 2px solid #5acfbc;
        border-radius: 5px;
        background-color: transparent;
        color: #5acfbc;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        text-decoration: none;
      }

      .btn-login:hover {
        background-color: #5acfbc;
        color: white;
      }

      .btn-register {
        padding: 8px 20px;
        border: 2px solid #5acfbc;
        border-radius: 5px;
        background-color: #5acfbc;
        color: white;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        text-decoration: none;
      }

      .btn-register:hover {
        background-color: #48b0a0;
        border-color: #48b0a0;
      }

      .menu-toggle {
        display: none;
        cursor: pointer;
        font-size: 1.5rem;
      }

      /* Hero Section */
      .hero {
        padding: 150px 0 80px;
        text-align: center;
        background-image: linear-gradient(
            0deg,
            rgba(0, 0, 0, 0.8) 50%,
            rgba(0, 0, 0, 0.3)
          ),
          url(./images/banner.jpg);
        background-size: 70px;
      }

      .hero h1 {
        font-size: 3rem;
        margin-bottom: 20px;
        color: #fff;
        backdrop-filter: blur(1px);
      }

      .hero p {
        font-size: 1.2rem;
        color: #fff;
        max-width: 700px;
        margin: 0 auto 40px;
        backdrop-filter: blur(1px);
      }

      .btn {
        display: inline-block;
        background-color: #5acfbc;
        color: white;
        padding: 12px 30px;
        border-radius: 5px;
        text-decoration: none;
        font-weight: 600;
        transition: background-color 0.3s;
      }

      .btn:hover {
        background-color: #48b0a0;
      }

      /* Key Features Section */
      .key-features {
        padding: 60px 0;
        background-color: white;
        border-bottom: 1px solid #eaeaea;
      }

      .features-heading {
        text-align: center;
        margin-bottom: 50px;
      }

      .features-heading h2 {
        font-size: 2.2rem;
        color: #333;
        margin-bottom: 15px;
      }

      .features-heading p {
        color: #666;
        max-width: 700px;
        margin: 0 auto;
      }

      .features-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;
      }

      .feature-box {
        text-align: center;
        padding: 30px 20px;
        border-radius: 10px;
        transition: all 0.3s ease;
        background-color: #f8f9fa;
        position: relative;
        overflow: hidden;
        z-index: 1;
      }

      .feature-box::before {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background-color: #5acfbc;
        transition: height 0.3s ease;
        z-index: -1;
      }

      .feature-box:hover::before {
        height: 100%;
      }

      .feature-box:hover {
        transform: translateY(-10px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      }

      .feature-box:hover .feature-icon,
      .feature-box:hover h3,
      .feature-box:hover p {
        color: white;
      }

      .feature-icon {
        font-size: 3rem;
        margin-bottom: 20px;
        color: #5acfbc;
        transition: color 0.3s ease;
      }

      .feature-box h3 {
        margin-bottom: 15px;
        font-size: 1.4rem;
        transition: color 0.3s ease;
      }

      .feature-box p {
        color: #666;
        transition: color 0.3s ease;
      }

      /* Why Us Section */
      .why-us {
        padding: 80px 0;
        background-color: #f8f9fa;
        position: relative;
      }

      .why-us::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%235acfbc' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
        opacity: 0.5;
        z-index: 0;
      }

      .why-us .container {
        position: relative;
        z-index: 1;
      }

      .why-us-heading {
        text-align: center;
        margin-bottom: 60px;
      }

      .why-us-heading h2 {
        font-size: 2.5rem;
        color: #333;
        margin-bottom: 15px;
        position: relative;
        display: inline-block;
      }

      .why-us-heading h2::after {
        content: "";
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 4px;
        background-color: #5acfbc;
      }

      .why-us-heading p {
        color: #666;
        max-width: 700px;
        margin: 0 auto;
        font-size: 1.1rem;
      }

      .solutions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-bottom: 60px;
      }

      .solution-item {
        background-color: white;
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
      }

      .solution-item:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        border-bottom: 4px solid #5acfbc;
      }

      .solution-icon {
        width: 60px;
        height: 60px;
        background-color: rgba(90, 207, 188, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        font-size: 1.8rem;
      }

      .solution-item h3 {
        font-size: 1.4rem;
        margin-bottom: 15px;
        color: #333;
      }

      .solution-item p {
        color: #666;
        margin-bottom: 20px;
        flex-grow: 1;
      }

      .trusted-by {
        text-align: center;
        margin-top: 60px;
      }

      .trusted-by h3 {
        font-size: 1.5rem;
        color: #333;
        margin-bottom: 30px;
        font-weight: 600;
      }

      .brand-logos {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        gap: 50px;
      }

      .brand-logo {
        display: flex;
        flex-direction: column;
        align-items: center;
        max-width: 200px;
      }

      .logo-container {
        width: 150px;
        height: 80px;
        background-color: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        margin-bottom: 15px;
        padding: 10px;
      }

      .logo-placeholder {
        font-size: 1.2rem;
        font-weight: 700;
        color: #333;
        text-align: center;
      }

      .brand-quote {
        font-style: italic;
        color: #666;
        font-size: 0.9rem;
        text-align: center;
      }

      .testimonial-author {
        font-weight: 600;
        color: #333;
        font-size: 0.9rem;
        margin-top: 5px;
      }

      /* Services Section */
      .services {
        padding: 80px 0;
        background-color: white;
      }

      .section-title {
        text-align: center;
        margin-bottom: 20px;
        font-size: 2.2rem;
      }

      .section-subtitle {
        text-align: center;
        margin-bottom: 60px;
        color: #666;
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
      }

      .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
      }

      .service-card {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s;
        display: flex;
        flex-direction: column;
        height: 100%;
      }

      .service-card:hover {
        transform: translateY(-10px);
      }

      .service-icon {
        font-size: 2.5rem;
        margin-bottom: 20px;
        color: #5acfbc;
      }

      .service-card h3 {
        margin-bottom: 15px;
        color: #333;
      }

      .service-card p {
        margin-bottom: 20px;
        flex-grow: 1;
      }

      .service-link {
        color: #5acfbc;
        text-decoration: none;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
      }

      .service-link:hover {
        text-decoration: underline;
      }

      /* Career Tools Section */
      .career-tools {
        padding: 80px 0;
        background-color: #f8f9fa;
      }

      .tools-container {
        margin-top: 50px;
      }

      .tool-category {
        margin-bottom: 50px;
      }

      .category-title {
        font-size: 1.5rem;
        color: #333;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #5acfbc;
      }

      .tools-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
      }

      .tool-card {
        background-color: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
        transition: all 0.3s;
        border-left: 4px solid #5acfbc;
      }

      .tool-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .tool-card h4 {
        margin-bottom: 10px;
        color: #333;
      }

      .tool-card p {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 15px;
      }

      .tool-link {
        display: inline-block;
        color: #5acfbc;
        font-size: 0.9rem;
        font-weight: 600;
        text-decoration: none;
      }

      .tool-link:hover {
        text-decoration: underline;
      }

      /* CTA Section */
      .cta {
        padding: 100px 0;
        background-color: #5acfbc;
        color: white;
        position: relative;
        overflow: hidden;
      }

      .cta::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.2;
      }

      .cta-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 50px;
        align-items: center;
        position: relative;
        z-index: 1;
      }

      .cta-content {
        text-align: left;
      }

      .cta h2 {
        font-size: 2.5rem;
        margin-bottom: 20px;
      }

      .cta p {
        font-size: 1.2rem;
        margin-bottom: 30px;
        opacity: 0.9;
      }

      .btn-white {
        background-color: white;
        color: #5acfbc;
        border: 2px solid white;
      }

      .btn-white:hover {
        background-color: transparent;
        color: white;
      }

      .contact-form {
        background-color: white;
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      }

      .form-title {
        color: #333;
        margin-bottom: 20px;
        font-size: 1.5rem;
        text-align: center;
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-label {
        display: block;
        margin-bottom: 8px;
        color: #333;
        font-weight: 500;
      }

      .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 1rem;
        transition: border-color 0.3s;
      }

      .form-control:focus {
        outline: none;
        border-color: #5acfbc;
        box-shadow: 0 0 0 2px rgba(90, 207, 188, 0.2);
      }

      textarea.form-control {
        min-height: 120px;
        resize: vertical;
      }

      .form-submit {
        background-color: #5acfbc;
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 5px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        width: 100%;
        font-size: 1rem;
      }

      .form-submit:hover {
        background-color: #48b0a0;
        transform: translateY(-2px);
      }

      /* Footer */
      footer {
        background-color: #333;
        color: white;
        padding: 60px 0 30px;
      }

      .footer-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 40px;
        margin-bottom: 40px;
      }

      .footer-column h3 {
        margin-bottom: 20px;
        font-size: 1.2rem;
      }

      .footer-column ul {
        list-style: none;
      }

      .footer-column ul li {
        margin-bottom: 10px;
      }

      .footer-column a {
        color: #ccc;
        text-decoration: none;
        transition: color 0.3s;
      }

      .footer-column a:hover {
        color: white;
      }

      .footer-bottom {
        text-align: center;
        padding-top: 30px;
        border-top: 1px solid #444;
      }

      /* Responsive Design */
      @media (max-width: 992px) {
        .auth-buttons {
          margin-left: 20px;
        }

        .nav-links li {
          margin-left: 20px;
        }

        .brand-logos {
          gap: 30px;
        }
      }

      @media (max-width: 768px) {
        .menu-toggle {
          display: block;
        }

        .nav-links {
          position: fixed;
          top: 70px;
          left: -100%;
          width: 100%;
          height: calc(100vh - 70px);
          background-color: white;
          flex-direction: column;
          align-items: center;
          padding-top: 40px;
          transition: left 0.3s;
        }

        .nav-links.active {
          left: 0;
        }

        .nav-links li {
          margin: 0 0 20px 0;
        }

        .auth-buttons {
          margin: 20px 0 0 0;
        }

        .hero h1 {
          font-size: 2.2rem;
        }

        .hero p {
          font-size: 1rem;
        }

        .section-title {
          font-size: 1.8rem;
        }

        .cta h2 {
          font-size: 2rem;
        }

        .why-us-heading h2 {
          font-size: 2rem;
        }

        .brand-logos {
          flex-direction: column;
          gap: 40px;
        }

        .cta-container {
          grid-template-columns: 1fr;
        }

        .cta-content {
          text-align: center;
        }
      }
    </style>
  </head>
  <body>
    <!-- Header -->
    <header>
      <div class="container">
        <nav>
          <div class="logo">
            <img src="./images/logo.jpg" alt="Career Scopes Logo" />
          </div>
          <ul class="nav-links">
            <li><a href="#home">Home</a></li>
            <li><a href="#about">About Us</a></li>
            <li><a href="#services">Services</a></li>
            <li><a href="#tools">Career Tools</a></li>
            <div class="auth-buttons">
              <a href="#login" class="btn-login">Login</a>
              <a href="#register" class="btn-register">Register</a>
            </div>
          </ul>
          <div class="menu-toggle">☰</div>
        </nav>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
      <div class="container">
        <h1>Empower Your Career Journey with CareerScopes</h1>
        <p>
          Personalized guidance, psychometric assessments, and career resources
          to help you achieve your goals.
        </p>
        <a href="./home.html" class="btn">Get Started</a>
      </div>
    </section>

    <!-- Key Features Section -->
    <section class="key-features">
      <div class="container">
        <div class="features-heading">
          <h2>Explore Our Key Features</h2>
          <p>
            Discover the tools and resources that will transform your career
            journey and help you achieve your professional goals.
          </p>
        </div>

        <div class="features-container">
          <div class="feature-box">
            <div class="feature-icon">📊</div>
            <h3>Career Assessments</h3>
            <p>
              Discover your strengths, interests, and ideal career path with our
              comprehensive assessment tools.
            </p>
          </div>

          <div class="feature-box">
            <div class="feature-icon">📈</div>
            <h3>Job Market Insights</h3>
            <p>
              Access real-time data and trends to make informed decisions about
              your career direction.
            </p>
          </div>

          <div class="feature-box">
            <div class="feature-icon">📝</div>
            <h3>Resume & Cover Templates</h3>
            <p>
              Stand out with professionally designed templates tailored to your
              industry and experience level.
            </p>
          </div>

          <div class="feature-box">
            <div class="feature-icon">🏆</div>
            <h3>Portfolio Builder</h3>
            <p>
              Showcase your work and achievements with our intuitive portfolio
              creation platform.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
      <div class="container">
        <h2 class="section-title">Our Services</h2>
        <p class="section-subtitle">
          We offer comprehensive career guidance services tailored to your
          unique professional journey, helping you navigate every stage of your
          career development.
        </p>

        <div class="services-grid">
          <div class="service-card">
            <div class="service-icon">🎯</div>
            <h3>Career Assessment & Planning</h3>
            <p>
              Discover your strengths, interests, and ideal career path through
              comprehensive assessments and personalized career planning
              sessions with our expert counselors.
            </p>
            <a href="#" class="service-link">Learn More →</a>
          </div>

          <div class="service-card">
            <div class="service-icon">📝</div>
            <h3>Resume & Portfolio Development</h3>
            <p>
              Stand out from the competition with professionally crafted
              resumes, cover letters, and portfolios that highlight your unique
              skills and experiences.
            </p>
            <a href="#" class="service-link">Learn More →</a>
          </div>

          <div class="service-card">
            <div class="service-icon">🗣️</div>
            <h3>Interview Preparation</h3>
            <p>
              Build confidence and master interview techniques through mock
              interviews, feedback sessions, and personalized coaching to help
              you succeed in any interview scenario.
            </p>
            <a href="#" class="service-link">Learn More →</a>
          </div>

          <div class="service-card">
            <div class="service-icon">🔄</div>
            <h3>Career Transition Support</h3>
            <p>
              Navigate career changes with confidence through targeted guidance,
              skill gap analysis, and strategic planning to help you
              successfully pivot to a new industry or role.
            </p>
            <a href="#" class="service-link">Learn More →</a>
          </div>

          <div class="service-card">
            <div class="service-icon">🌱</div>
            <h3>Professional Development</h3>
            <p>
              Accelerate your career growth with customized development plans,
              skill-building workshops, and ongoing coaching to help you reach
              your professional goals.
            </p>
            <a href="#" class="service-link">Learn More →</a>
          </div>
        </div>
      </div>
    </section>

    <!-- Career Tools Section -->
    <section class="career-tools" id="tools">
      <div class="container">
        <h2 class="section-title">Career Tools</h2>
        <p class="section-subtitle">
          Access our specialized career tools designed to help you navigate
          every aspect of your professional journey, from self-discovery to
          career advancement.
        </p>

        <div class="tools-container">
          <!-- Assessment Tools Category -->
          <div class="tool-category">
            <h3 class="category-title">Assessment Tools</h3>
            <div class="tools-grid">
              <div class="tool-card">
                <h4>Career Interest Profiler</h4>
                <p>
                  Discover career paths that align with your interests, values,
                  and personality traits.
                </p>
                <a href="#" class="tool-link">Access Tool →</a>
              </div>

              <div class="tool-card">
                <h4>Skills Assessment</h4>
                <p>
                  Identify your strengths and areas for development with our
                  comprehensive skills evaluation.
                </p>
                <a href="#" class="tool-link">Access Tool →</a>
              </div>

              <div class="tool-card">
                <h4>Work Values Inventory</h4>
                <p>
                  Understand what motivates you professionally and find careers
                  that match your values.
                </p>
                <a href="#" class="tool-link">Access Tool →</a>
              </div>
            </div>
          </div>

          <!-- Job Search Tools Category -->
          <div class="tool-category">
            <h3 class="category-title">Job Search Tools</h3>
            <div class="tools-grid">
              <div class="tool-card">
                <h4>Resume Builder</h4>
                <p>
                  Create professional, ATS-friendly resumes with our guided
                  template system.
                </p>
                <a href="#" class="tool-link">Access Tool →</a>
              </div>

              <div class="tool-card">
                <h4>Cover Letter Generator</h4>
                <p>
                  Craft compelling cover letters tailored to specific job
                  applications and industries.
                </p>
                <a href="#" class="tool-link">Access Tool →</a>
              </div>

              <div class="tool-card">
                <h4>Interview Simulator</h4>
                <p>
                  Practice common and industry-specific interview questions with
                  AI-powered feedback.
                </p>
                <a href="#" class="tool-link">Access Tool →</a>
              </div>

              <div class="tool-card">
                <h4>Job Search Tracker</h4>
                <p>
                  Organize your job applications, follow-ups, and interviews in
                  one convenient dashboard.
                </p>
                <a href="#" class="tool-link">Access Tool →</a>
              </div>
            </div>
          </div>

          <!-- Professional Development Tools Category -->
          <div class="tool-category">
            <h3 class="category-title">Professional Development Tools</h3>
            <div class="tools-grid">
              <div class="tool-card">
                <h4>Skill Gap Analyzer</h4>
                <p>
                  Compare your current skills to job requirements and create a
                  development plan.
                </p>
                <a href="#" class="tool-link">Access Tool →</a>
              </div>

              <div class="tool-card">
                <h4>Career Path Planner</h4>
                <p>
                  Map out your long-term career trajectory with milestone
                  planning and goal setting.
                </p>
                <a href="#" class="tool-link">Access Tool →</a>
              </div>

              <div class="tool-card">
                <h4>Networking Strategy Builder</h4>
                <p>
                  Develop effective networking approaches tailored to your
                  industry and career goals.
                </p>
                <a href="#" class="tool-link">Access Tool →</a>
              </div>
            </div>
          </div>

          <!-- Specialized Tools Category -->
          <div class="tool-category">
            <h3 class="category-title">Specialized Tools</h3>
            <div class="tools-grid">
              <div class="tool-card">
                <h4>Salary Negotiation Simulator</h4>
                <p>
                  Practice negotiation scenarios and develop strategies to
                  maximize your compensation.
                </p>
                <a href="#" class="tool-link">Access Tool →</a>
              </div>

              <div class="tool-card">
                <h4>Personal Brand Builder</h4>
                <p>
                  Create a cohesive professional brand across platforms to
                  enhance your visibility.
                </p>
                <a href="#" class="tool-link">Access Tool →</a>
              </div>

              <div class="tool-card">
                <h4>Career Transition Roadmap</h4>
                <p>
                  Navigate career changes with a customized plan for entering a
                  new industry or role.
                </p>
                <a href="#" class="tool-link">Access Tool →</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Why Us Section -->
    <section class="why-us" id="about">
      <div class="container">
        <div class="why-us-heading">
          <h2>Why Us</h2>
          <p>
            At Career Scopes, we believe in a personalized approach to career
            development. Our solutions are designed to meet your unique needs
            and help you navigate your professional journey with confidence.
          </p>
        </div>

        <div class="solutions-grid">
          <div class="solution-item">
            <div class="solution-icon">👤</div>
            <h3>Personalized Career Assessment</h3>
            <p>
              Unlike generic career tests, our assessments are tailored to your
              unique background, skills, and aspirations. We analyze over 100
              data points to provide truly personalized guidance that aligns
              with your goals.
            </p>
          </div>

          <div class="solution-item">
            <div class="solution-icon">📊</div>
            <h3>Real-time Job Market Insights</h3>
            <p>
              Our AI-powered analytics platform monitors industry trends, salary
              benchmarks, and skill demands across thousands of companies. Get
              actionable insights that help you make strategic career decisions.
            </p>
          </div>

          <div class="solution-item">
            <div class="solution-icon">💼</div>
            <h3>Professional Portfolio Building Tools</h3>
            <p>
              Create stunning digital portfolios that showcase your work and
              achievements. Our intuitive tools help you highlight your
              expertise and stand out to potential employers or clients.
            </p>
          </div>

          <div class="solution-item">
            <div class="solution-icon">🎓</div>
            <h3>Expert Curated Resources</h3>
            <p>
              Access our library of training pathways, courses, and resources
              curated by industry experts. Our learning materials are constantly
              updated to reflect the latest skills and knowledge demanded by
              employers.
            </p>
          </div>

          <div class="solution-item">
            <div class="solution-icon">👥</div>
            <h3>Supportive Community</h3>
            <p>
              Join a thriving community of like-minded professionals who share
              your ambition to grow and succeed. Network, collaborate, and learn
              from peers who understand your career challenges.
            </p>
          </div>
        </div>

        <div class="trusted-by">
          <h3>Trusted By Industry Leaders</h3>
          <div class="brand-logos">
            <div class="brand-logo">
              <div class="logo-container">
                <img width="100" height="100" src="./images/4enmhULP.jpg" />
              </div>
            </div>

            <div class="brand-logo">
              <div class="logo-container">
                <img width="100" height="100" src="./images/K6mE6NqT.jpg" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section with Contact Form -->
    <section class="cta" id="contact">
      <div class="container">
        <div class="cta-container">
          <div class="cta-content">
            <h2>Ready to Transform Your Career?</h2>
            <p>
              Join thousands of professionals who have already advanced their
              careers with Career Scopes guidance.
            </p>
            <a href="#" class="btn btn-white">Book a Consultation</a>
          </div>

          <div class="contact-form">
            <h3 class="form-title">Get in Touch</h3>
            <form id="contactForm" action="#" method="POST">
              <div class="form-group">
                <label for="name" class="form-label">Full Name</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  class="form-control"
                  placeholder="Your name"
                  required
                />
              </div>

              <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  class="form-control"
                  placeholder="Your email"
                  required
                />
              </div>

              <div class="form-group">
                <label for="phone" class="form-label">Phone Number</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  class="form-control"
                  placeholder="Your phone number"
                />
              </div>

              <div class="form-group">
                <label for="subject" class="form-label">Subject</label>
                <select
                  id="subject"
                  name="subject"
                  class="form-control"
                  required
                >
                  <option value="" selected disabled>Select a subject</option>
                  <option value="career-assessment">Career Assessment</option>
                  <option value="resume-development">Resume Development</option>
                  <option value="interview-preparation">
                    Interview Preparation
                  </option>
                  <option value="career-transition">Career Transition</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div class="form-group">
                <label for="message" class="form-label">Message</label>
                <textarea
                  id="message"
                  name="message"
                  class="form-control"
                  placeholder="How can we help you?"
                  required
                ></textarea>
              </div>

              <button type="submit" class="form-submit">Send Message</button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer>
      <div class="container">
        <div class="footer-content">
          <div class="footer-column">
            <h3>CareerScops</h3>
          </div>
          <div class="footer-column">
            <ul>
              <li><a href="#">About</a></li>
              <li><a href="#">Jobs</a></li>
              <li><a href="#">Docks</a></li>
            </ul>
          </div>
          <div class="footer-column">
            <ul>
              <li><a href="#">Terms & Conditions</a></li>
              <li><a href="#">Privacy Policy</a></li>
              <li><a href="#">Cookie Policy</a></li>
            </ul>
          </div>
          <div class="footer-column">
            <h3>Contact</h3>
            <ul>
              <li><a href="#"><EMAIL></a></li>
              <li><a href="#">+****************</a></li>
              <li><a href="#">123 Professional Ave, Suite 100</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2025 Career Scopes. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <script>
      // Simple JavaScript for mobile menu toggle
      document
        .querySelector(".menu-toggle")
        .addEventListener("click", function () {
          document.querySelector(".nav-links").classList.toggle("active");
        });

      // Simple form validation and submission
      document
        .getElementById("contactForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();
          // Here you would typically send the form data to a server
          alert("Thank you for your message! We will get back to you soon.");
          this.reset();
        });
    </script>
  </body>
</html>
