<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Resume Writing - CareerScopes</title>
    <link rel="stylesheet" href="./css/styles.css" />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
    />
    <style>
      .header {
        padding: 20px 0;
        background-color: #fff;
      }

      .header .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      /* Resume Writing Specific Styles */
      .resume-hero {
        background: linear-gradient(135deg, #7fb9b1 0%, #a8d5d1 100%);
        padding: 80px 0;
        border-radius: 20px;
        margin: 20px;
        position: relative;
        overflow: hidden;
      }

      .resume-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url('./images/Foreground.jpeg');
        background-size: cover;
        background-position: center;
        opacity: 0.3;
        z-index: 1;
      }

      .resume-hero .container {
        position: relative;
        z-index: 2;
        text-align: center;
      }

      .resume-hero h1 {
        color: white;
        font-size: 48px;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        font-style: italic;
      }

      .main-content {
        padding: 60px 0;
        text-align: center;
      }

      .main-title {
        font-size: 36px;
        font-weight: 700;
        color: #333;
        margin-bottom: 40px;
        line-height: 1.2;
      }

      .play-button {
        width: 80px;
        height: 80px;
        background-color: #333;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 40px auto;
        cursor: pointer;
        transition: transform 0.3s;
      }

      .play-button:hover {
        transform: scale(1.1);
      }

      .play-button::after {
        content: '';
        width: 0;
        height: 0;
        border-left: 20px solid white;
        border-top: 12px solid transparent;
        border-bottom: 12px solid transparent;
        margin-left: 5px;
      }

      .services-section {
        padding: 40px 0;
      }

      .services-title {
        font-size: 32px;
        font-weight: 700;
        color: #333;
        margin-bottom: 40px;
      }

      .services-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
        max-width: 900px;
        margin: 0 auto;
      }

      .service-card {
        background-color: #7fb9b1;
        padding: 40px 20px;
        border-radius: 15px;
        text-align: center;
        color: #333;
        transition: transform 0.3s;
      }

      .service-card:hover {
        transform: translateY(-5px);
      }

      .service-tier {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 10px;
      }

      .service-description {
        font-size: 16px;
        font-weight: 500;
        line-height: 1.4;
      }

      .upload-section {
        padding: 60px 0;
        background-color: #f8f9fa;
        margin: 40px 0;
      }

      .upload-container {
        max-width: 600px;
        margin: 0 auto;
        text-align: center;
      }

      .upload-box {
        border: 2px dashed #ccc;
        border-radius: 10px;
        padding: 60px 40px;
        background-color: white;
        cursor: pointer;
        transition: border-color 0.3s;
      }

      .upload-box:hover {
        border-color: #7fb9b1;
      }

      .upload-icon {
        font-size: 48px;
        color: #999;
        margin-bottom: 20px;
      }

      .upload-text {
        font-size: 18px;
        color: #666;
        font-weight: 500;
      }

      .file-input {
        display: none;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .header .container {
          flex-direction: column;
          gap: 20px;
        }

        .resume-hero h1 {
          font-size: 36px;
        }

        .main-title {
          font-size: 28px;
        }

        .services-grid {
          grid-template-columns: 1fr;
          gap: 20px;
          padding: 0 20px;
        }

        .resume-hero {
          margin: 10px;
          padding: 60px 0;
        }

        .upload-box {
          padding: 40px 20px;
          margin: 0 20px;
        }
      }

      @media (max-width: 576px) {
        .resume-hero h1 {
          font-size: 28px;
        }

        .main-title {
          font-size: 24px;
        }

        .play-button {
          width: 60px;
          height: 60px;
        }

        .play-button::after {
          border-left: 15px solid white;
          border-top: 9px solid transparent;
          border-bottom: 9px solid transparent;
        }
      }
    </style>
  </head>
  <body>
    <!-- Header Section -->
    <div class="header">
      <div class="container">
        <div class="logo">
          <a href="./index.html">
            <img src="./images/logo.jpg" alt="careerScops" />
          </a>
        </div>
        <nav>
          <ul>
            <li><a href="./index.html">Home</a></li>
            <li><a href="./about-us.html">About us</a></li>
            <li><a href="./resume_writing.html" class="active">Services</a></li>
            <li><a href="./testimonials.html">Testimonials</a></li>
          </ul>
        </nav>
        <div class="auth-buttons">
          <a href="./login.html" class="login-btn">Login</a>
          <a href="./register.html" class="register-btn">Register</a>
        </div>
      </div>
    </div>

    <!-- Resume Writing Hero Section -->
    <section class="resume-hero">
      <div class="container">
        <h1>Resume Writing</h1>
      </div>
    </section>

    <!-- Main Content Section -->
    <section class="main-content">
      <div class="container">
        <h2 class="main-title">Professional documents that get<br>results</h2>
        
        <div class="play-button" onclick="playVideo()"></div>
        
        <div class="services-section">
          <h2 class="services-title">Our Services</h2>
          
          <div class="services-grid">
            <div class="service-card">
              <div class="service-tier">FREE</div>
              <div class="service-description">Resume review</div>
            </div>
            
            <div class="service-card">
              <div class="service-tier">Gold</div>
              <div class="service-description">Resume review<br>+ Feedback</div>
            </div>
            
            <div class="service-card">
              <div class="service-tier">Premium</div>
              <div class="service-description">Complete rewrite<br>+ cover letter</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Upload Section -->
    <section class="upload-section">
      <div class="container">
        <div class="upload-container">
          <div class="upload-box" onclick="document.getElementById('fileInput').click()">
            <div class="upload-icon">📄</div>
            <div class="upload-text">Upload Resume / CV</div>
            <input type="file" id="fileInput" class="file-input" accept=".pdf,.doc,.docx" onchange="handleFileUpload(this)">
          </div>
        </div>
      </div>
    </section>

    <!-- Footer Section -->
    <footer>
      <div class="container">
        <div class="footer-top">
          <div class="footer-info">
            <h3>CareerScopes</h3>
            <p>
              Career Scopes would like to acknowledge the traditional custodians
              of the land on which we are based, the Aboriginal people of the
              Kulin Nation.
            </p>
            <p>
              We also pay our respects to their Elders, past, present and
              future, whose cultures and customs have nurtured and continue to
              nurture this land.
            </p>
            <p>We pay our respects to Elders past, present and future.</p>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h4>About</h4>
              <ul>
                <li><a href="#">JOBS</a></li>
                <li><a href="#">FAQS</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h4>Legal</h4>
              <ul>
                <li><a href="#">Terms and Conditions</a></li>
                <li><a href="#">Privacy Policy</a></li>
                <li><a href="#">Cookie Policy</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h4>Contact us</h4>
              <div class="social-icons">
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                    ></path>
                  </svg>
                  <span>0421 594 877</span>
                </a>
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                    ></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                  <span style="font-size: 0.75rem"
                    ><EMAIL></span
                  >
                </a>
                <a href="#" class="social-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"
                    ></path>
                    <rect x="2" y="9" width="4" height="12"></rect>
                    <circle cx="4" cy="4" r="2"></circle>
                  </svg>
                  <span style="font-size: 1rem"
                    >www.linkedin.com/in/lijesla</span
                  >
                </a>
              </div>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <div class="flags">
            <img
              src="./images/image 13.png"
              alt="Aboriginal Flag"
              class="flag"
            />
            <img
              src="./images/image 14.png"
              alt="Torres Strait Islander Flag"
              class="flag"
            />
          </div>
          <div class="copyright">
            © CareerScope - All Rights Reserved. Love From Australia
          </div>
        </div>
      </div>
    </footer>

    <script>
      function playVideo() {
        alert('Video functionality would be implemented here');
      }

      function handleFileUpload(input) {
        if (input.files && input.files[0]) {
          const fileName = input.files[0].name;
          alert(`File "${fileName}" uploaded successfully!`);
        }
      }
    </script>
  </body>
</html>
